# Istem Formu Permission Fixes

## Issue:
Non-admin users were getting "insufficient permissions" error when accessing patient details page, specifically when trying to fetch completed istem formu records.

## Root Cause:
The `getCompletedIstemFormuByPatient` and `getWaitingIstemFormuByPatient` functions were trying to:
1. **Query all users collection** - Non-admin users don't have permission to list all users
2. **Access all users' istem-formu subcollections** - Non-admin users can only access their own subcollections

## Solution:
Modified both functions to accept an optional `userId` parameter:

### For Non-Admin Users:
- Pass `userId` parameter to query only their own `users/{userId}/istem-formu` subcollection
- This respects the Firestore security rules that allow users to access only their own data

### For Admin Users:
- Don't pass `userId` parameter (undefined) to maintain existing behavior
- Query all users' subcollections for comprehensive view

## Files Modified:

### 1. `lib/services/istemFormuService.ts`
- Updated `getCompletedIstemFormuByPatient(patientId, userId?)` 
- Updated `getWaitingIstemFormuByPatient(patientId, userId?)`
- Added conditional logic based on userId parameter

### 2. `app/dashboard/hastalar/[id]/page.tsx`
- Added `user` to useAuth destructuring
- Updated `fetchCompletedForms` to pass `user?.uid` for non-admin users
- Updated dependency array to include `isAdmin` and `user?.uid`

### 3. `components/hastalar/ScanUploadModal.tsx`
- Updated `fetchWaitingForms` to pass `user?.uid` for non-admin users
- Updated dependency array to include `isAdmin` and `user?.uid`

## Expected Behavior:

### Non-Admin Users:
- ✅ Can access patient details page without permission errors
- ✅ See only their own istem formu records for the patient
- ✅ Maintain security - cannot access other doctors' forms

### Admin Users:
- ✅ See all istem formu records from all doctors for the patient
- ✅ Maintain full access as before

## Security Maintained:
- Users can only query their own istem-formu subcollections
- Patient access control is still enforced through the patients collection rules
- Admin users retain full cross-user access
- No changes to Firestore security rules needed - existing rules support this approach