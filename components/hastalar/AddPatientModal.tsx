'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { X, User, Calendar as CalendarIcon, Mail, Save, Edit } from 'lucide-react';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import tr from 'react-phone-input-2/lang/tr.json';
import { format } from 'date-fns';
import { tr as dateFnsTr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { patientFormSchema, type PatientFormData } from '@/lib/schemas/validation';

export interface AddPatientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (patientData: PatientFormData) => void;
  patientData?: PatientFormData | null; // Optional patient data for editing
  isEditing?: boolean; // Flag to determine if we're editing
}

export default function AddPatientModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  patientData = null, 
  isEditing = false 
}: AddPatientModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { register, handleSubmit, control, formState: { errors }, reset } = useForm<PatientFormData>({
    resolver: zodResolver(patientFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      tcKimlik: '',
      birthDate: undefined,
      gender: 'Erkek' as const,
      phone: '',
      email: '',
      address: ''
    }
  });

  // Populate form data when editing or when modal opens
  useEffect(() => {
    if (isOpen) {
      if (isEditing && patientData) {
        // Use reset to populate the form with patient data
        reset(patientData);
      } else {
        // Reset to default values for a new patient
        reset({
          firstName: '',
          lastName: '',
          tcKimlik: '',
          birthDate: undefined,
          gender: 'Erkek',
          phone: '',
          email: '',
          address: ''
        });
      }
    }
  }, [isOpen, isEditing, patientData, reset]);

  const handleFormSubmit = async (data: PatientFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      handleClose();
    } catch (error) {
      console.error('Error submitting patient form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setIsSubmitting(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={handleClose} />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-2xl bg-white rounded-2xl shadow-2xl transform transition-all animate-slideUp">
          {/* Header */}
          <div className={`${isEditing ? 'bg-gradient-to-r from-emerald-500 to-emerald-600' : 'bg-gradient-to-r from-blue-500 to-blue-600'} rounded-t-2xl px-8 py-6`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                  {isEditing ? (
                    <Edit className="w-5 h-5 text-white" />
                  ) : (
                    <User className="w-5 h-5 text-white" />
                  )}
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    {isEditing ? 'Hasta Bilgilerini Düzenle' : 'Yeni Hasta Ekle'}
                  </h2>
                  <p className={`${isEditing ? 'text-emerald-100' : 'text-blue-100'}`}>
                    {isEditing ? 'Hasta bilgilerini güncelleyin' : 'Hasta bilgilerini girin'}
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer hover:scale-110 transform"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(handleFormSubmit)} className="p-8">
            <div className="space-y-6">
              {/* Name and Surname Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* First Name */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Ad <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    {...register("firstName")}
                    placeholder="Hasta adı"
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 ${
                      errors.firstName 
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                    } bg-white shadow-sm`}
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                  )}
                </div>

                {/* Last Name */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Soyad <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    {...register("lastName")}
                    placeholder="Hasta soyadı"
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 ${
                      errors.lastName 
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                    } bg-white shadow-sm`}
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              {/* TC Kimlik and Birth Date Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* TC Kimlik */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    TC Kimlik No <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    {...register("tcKimlik")}
                    placeholder="11 haneli TC kimlik numarası"
                    maxLength={11}
                    className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 ${
                      errors.tcKimlik 
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                    } bg-white shadow-sm`}
                  />
                  {errors.tcKimlik && (
                    <p className="mt-1 text-sm text-red-600">{errors.tcKimlik.message}</p>
                  )}
                </div>

                {/* Birth Date */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Doğum Tarihi <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Controller
                      name="birthDate"
                      control={control}
                      render={({ field }) => (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              className={cn(
                                "w-full h-12 px-4 py-3 pl-12 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-pointer hover:border-blue-400 justify-start text-left font-normal",
                                !field.value && "text-gray-500",
                                errors.birthDate 
                                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                                  : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',
                                "bg-white shadow-sm hover:bg-white"
                              )}
                            >
                              <CalendarIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              <span className="ml-8 text-base">
                                {field.value ? (
                                  format(field.value, "dd/MM/yyyy", { locale: dateFnsTr })
                                ) : (
                                  "Doğum tarihi seçiniz"
                                )}
                              </span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() || date < new Date("1900-01-01")
                              }
                              captionLayout="dropdown"
                              locale={dateFnsTr}
                              formatters={{
                                formatMonthDropdown: (date) => 
                                  date.toLocaleDateString('tr-TR', { month: 'long' }),
                                formatYearDropdown: (date) => 
                                  date.getFullYear().toString(),
                              }}
                              autoFocus
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    />
                  </div>
                  {errors.birthDate && (
                    <p className="mt-1 text-sm text-red-600">{errors.birthDate.message}</p>
                  )}
                </div>
              </div>

              {/* Gender */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Cinsiyet <span className="text-red-500">*</span>
                </label>
                <Controller
                  name="gender"
                  control={control}
                  render={({ field }) => (
                    <Select
                      key={field.value} // Add key to force re-render on value change
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <SelectTrigger
                        className={cn(
                          "w-full px-4 py-6 h-12 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-pointer hover:border-blue-400",
                          errors.gender
                            ? "border-red-300 focus:ring-red-500 focus:border-red-500"
                            : "border-gray-300 focus:ring-blue-500 focus:border-blue-500",
                          "bg-white shadow-sm"
                        )}
                      >
                        <SelectValue placeholder="Cinsiyet seçiniz" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Erkek">Erkek</SelectItem>
                        <SelectItem value="Kadın">Kadın</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.gender && (
                  <p className="mt-1 text-sm text-red-600">{errors.gender.message}</p>
                )}
              </div>

              {/* Phone and Email Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Phone */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Telefon <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Controller
                      name="phone"
                      control={control}
                      render={({ field }) => (
                        <PhoneInput
                          country={'tr'}
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Telefon numarası giriniz"
                          inputProps={{
                            name: 'phone',
                            required: true,
                            className: `w-full px-4 py-3 pl-14 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 ${
                              errors.phone 
                                ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                            } bg-white shadow-sm`
                          }}
                          containerClass="w-full"
                          buttonClass={`border rounded-l-xl hover:border-blue-400 transition-all bg-white px-3 shadow-sm ${
                            errors.phone 
                              ? 'border-red-300 hover:border-red-300' 
                              : 'border-gray-300 hover:border-blue-400'
                          }`}
                          dropdownClass="rounded-xl shadow-2xl border-gray-200 max-h-48 overflow-y-auto"
                          searchClass="px-3 py-2 border-gray-300 rounded-lg text-sm mx-2 my-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          specialLabel=""
                          countryCodeEditable={false}
                          localization={tr}
                        />
                      )}
                    />
                  </div>
                  {errors.phone && (
                    <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">E-posta</label>
                  <div className="relative">
                    <input
                      type="email"
                      {...register("email")}
                      placeholder="<EMAIL>"
                      className={`w-full px-4 py-3 pl-12 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 ${
                        errors.email 
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                          : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                      } bg-white shadow-sm`}
                    />
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  </div>
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>
              </div>

              {/* Address */}
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Adres
                </label>
                <textarea
                  {...register("address")}
                  placeholder="Hasta adresini giriniz"
                  rows={3}
                  className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 resize-none ${
                    errors.address 
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  } bg-white shadow-sm`}
                />
                {errors.address && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                )}
              </div>
            </div>

            {/* Footer Buttons */}
            <div className="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="px-6 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-xl transition-colors font-semibold cursor-pointer hover:scale-105 transform disabled:opacity-50 disabled:cursor-not-allowed"
              >
                İptal
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`group flex items-center space-x-2 px-6 py-3 ${
                  isEditing 
                    ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700' 
                    : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
                } text-white rounded-xl transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                <Save className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span>{isSubmitting ? (isEditing ? 'Güncelleniyor...' : 'Kaydediliyor...') : (isEditing ? 'Güncelle' : 'Hastayı Kaydet')}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 