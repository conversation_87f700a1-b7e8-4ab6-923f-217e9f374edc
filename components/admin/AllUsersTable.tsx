'use client';

import { useState } from 'react';
import { User, Mail, Calendar, Shield, UserCheck, Trash2, Crown, Users } from 'lucide-react';
import { toast } from 'sonner';
import { User as UserType } from '@/lib/types';
import { userService } from '@/lib/services/userService';
import { adminService } from '@/lib/services/adminService';
import { formatDateTime } from '@/lib/utils';
import { useUser } from '@/lib/hooks/useUser';
import { useDentoConfirm } from '@/lib/hooks/useDentoConfirm';
import UserDetailsModal from './UserDetailsModal';
import Pagination from '@/components/ui/Pagination';

interface AllUsersTableProps {
  users: UserType[];
  loading: boolean;
  searchQuery?: string;
  onSearchChange?: (value: string) => void;
  currentPage?: number;
  totalPages?: number;
  totalUsers?: number;
  itemsPerPage?: number;
  onPageChange?: (page: number) => void;
  onRefresh?: () => void;
}

// Helper function to get initials
const getInitials = (firstName: string, lastName: string) => {
  return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
};

// Helper function to get role badge
const getRoleBadge = (role: string, isAdmin: boolean = false) => {
  // If user is admin, show "Admin" regardless of role
  if (isAdmin) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
        Admin
      </span>
    );
  }

  const roleConfig = {
    doctor: { label: 'Doktor', color: 'bg-blue-100 text-blue-800' },
    assistant: { label: 'Asistan', color: 'bg-green-100 text-green-800' }
  };

  const config = roleConfig[role as keyof typeof roleConfig] || { label: role, color: 'bg-gray-100 text-gray-800' };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

export default function AllUsersTable({
  users,
  loading,
  searchQuery = '',
  onSearchChange,
  currentPage = 1,
  totalPages = 1,
  totalUsers = 0,
  itemsPerPage = 10,
  onPageChange,
  onRefresh
}: AllUsersTableProps) {
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { userProfile, isAdmin } = useUser();
  const { confirm, ConfirmModal } = useDentoConfirm();

  // If user is not admin, don't render the component
  if (!isAdmin) {
    return null;
  }

  // Delete user
  const handleDeleteUser = async (user: UserType) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Sil',
      message: `${user.firstName} ${user.lastName} kullanıcısını kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve kullanıcının tüm verileri silinecektir.`,
      type: 'delete',
      confirmText: 'Sil'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(user.id));
    try {
      await userService.delete(user.id);
      toast.success(`${user.firstName} ${user.lastName} kullanıcısı başarıyla silindi.`);
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Kullanıcı silinirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(user.id);
        return newSet;
      });
    }
  };

  // Make user admin
  const handleMakeAdmin = async (user: UserType) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Admin Yap',
      message: `${user.firstName} ${user.lastName} kullanıcısını admin yapmak istediğinizden emin misiniz? Bu kullanıcı tüm admin yetkilerine sahip olacaktır.`,
      type: 'approve',
      confirmText: 'Admin Yap'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(user.id));
    try {
      await adminService.setAdminClaim(user.id, true);
      toast.success(`${user.firstName} ${user.lastName} kullanıcısı admin yapıldı.`);
      onRefresh?.();
    } catch (error) {
      console.error('Error making user admin:', error);
      toast.error('Kullanıcı admin yapılırken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(user.id);
        return newSet;
      });
    }
  };

  // Remove admin privileges
  const handleRemoveAdmin = async (user: UserType) => {
    const confirmed = await confirm({
      title: 'Admin Yetkisini Kaldır',
      message: `${user.firstName} ${user.lastName} kullanıcısının admin yetkilerini kaldırmak istediğinizden emin misiniz? Bu kullanıcı artık admin yetkilerine sahip olmayacaktır.`,
      type: 'disapprove',
      confirmText: 'Yetkiyi Kaldır'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(user.id));
    try {
      await adminService.setAdminClaim(user.id, false);
      toast.success(`${user.firstName} ${user.lastName} kullanıcısının admin yetkileri kaldırıldı.`);
      onRefresh?.();
    } catch (error) {
      console.error('Error removing admin privileges:', error);
      toast.error('Admin yetkileri kaldırılırken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(user.id);
        return newSet;
      });
    }
  };

  const isCurrentUser = (userId: string) => {
    return userProfile?.id === userId;
  };

  const isUserAdmin = (userId: string) => {
    if (isCurrentUser(userId)) {
      return isAdmin;
    }
    const user = users.find(u => u.id === userId);
    return user?.isAdmin || false;
  };

  const handleUserClick = (user: UserType) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  return (
    <>
      <ConfirmModal />
      <UserDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        user={selectedUser}
      />
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          Tüm Kullanıcılar ({totalUsers})
        </h2>
        {onSearchChange && (
          <div className="flex items-center space-x-4">
            {/* Search Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-96 pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Ad, e-posta veya telefon ile ara..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
              />
            </div>
          </div>
        )}
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-blue-50">
            <tr>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <span>Kullanıcı</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>İletişim</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Rol</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>Kayıt Tarihi</span>
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                <div className="flex items-center space-x-2 justify-end">
                  <UserCheck className="w-4 h-4" />
                  <span>İşlemler</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={5} className="px-6 py-20 text-center">
                  <div className="flex items-center justify-center text-gray-500">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Yükleniyor...</span>
                  </div>
                </td>
              </tr>
            ) : users.length > 0 ? (
              users.map((user) => (
                <tr 
                  key={user.id} 
                  className="hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => handleUserClick(user)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">{getInitials(user.firstName, user.lastName)}</span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{user.email}</div>
                    <div className="text-sm text-gray-500">{user.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getRoleBadge(user.role, isUserAdmin(user.id))}
                      {isCurrentUser(user.id) && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Siz
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDateTime(user.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end space-x-2">
                      {processingIds.has(user.id) ? (
                        <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <>
                          {isUserAdmin(user.id) ? (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                if (!isCurrentUser(user.id)) {
                                  handleRemoveAdmin(user);
                                }
                              }}
                              disabled={isCurrentUser(user.id)}
                              className={`p-2 rounded-full transition-colors cursor-pointer ${
                                isCurrentUser(user.id)
                                  ? "text-gray-400 cursor-not-allowed"
                                  : "text-orange-600 hover:bg-orange-100"
                              }`}
                              title={isCurrentUser(user.id) ? "Kendi admin yetkinizi kaldıramazsınız" : "Admin Yetkisini Kaldır"}
                            >
                              <Crown className="w-5 h-5" />
                            </button>
                          ) : (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                if (!isCurrentUser(user.id)) {
                                  handleMakeAdmin(user);
                                }
                              }}
                              disabled={isCurrentUser(user.id)}
                              className={`p-2 rounded-full transition-colors cursor-pointer ${
                                isCurrentUser(user.id)
                                  ? "text-gray-400 cursor-not-allowed"
                                  : "text-purple-600 hover:bg-purple-100"
                              }`}
                              title={isCurrentUser(user.id) ? "Kendinizi admin yapamazsınız" : "Kullanıcıyı Admin Yap"}
                            >
                              <Crown className="w-5 h-5" />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (!isCurrentUser(user.id)) {
                                handleDeleteUser(user);
                              }
                            }}
                            disabled={isCurrentUser(user.id)}
                            className={`p-2 rounded-full transition-colors cursor-pointer ${
                              isCurrentUser(user.id)
                                ? "text-gray-400 cursor-not-allowed"
                                : "text-red-600 hover:bg-red-100"
                            }`}
                            title={isCurrentUser(user.id) ? "Kendinizi silemezsiniz" : "Kullanıcıyı Sil"}
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-20 text-center text-gray-500">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8 text-gray-400" />
                    </div>
                    <p className="text-lg font-medium text-gray-900 mb-2">Kullanıcı Bulunamadı</p>
                    <p className="text-gray-500">Arama kriterlerine uyan kullanıcı bulunamadı.</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalUsers}
        itemsPerPage={itemsPerPage}
        onPageChange={onPageChange || (() => {})}
      />
    </div>
    </>
  );
} 