'use client';

import { X, User, Mail, Calendar, Clock, CheckCircle, XCircle } from 'lucide-react';
import { PendingUser } from '@/lib/types';
import { formatDateTime } from '@/lib/utils';
import { useDentoConfirm } from '@/lib/hooks/useDentoConfirm';
import { useUser } from '@/lib/hooks/useUser';
import { toast } from 'sonner';
import { pendingUserService } from '@/lib/services/pendingUserService';
import DentoButtonSecondary from '@/components/dentoui/DentoButtonSecondary';

const Section: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode; className?: string }> = ({ icon, title, children, className }) => (
  <div className={`bg-white rounded-xl p-5 border border-gray-200/80 shadow-sm ${className}`}>
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
    </div>
    <div>
      {children}
    </div>
  </div>
);

const InfoItem: React.FC<{ label: string; value: React.ReactNode; }> = ({ label, value }) => (
  <div className="py-2 border-b border-gray-100 last:border-b-0">
    <p className="text-sm font-medium text-gray-600 mb-0.5">{label}</p>
    <div className="text-sm text-gray-800">{value}</div>
  </div>
);

export interface PendingUserDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  pendingUser: PendingUser | null;
  onUserUpdated?: () => void;
}

// Helper function to get role badge
const getRoleBadge = (role: string) => {
  const roleConfig = {
    doctor: { label: 'Doktor', color: 'bg-blue-100 text-blue-800' },
    assistant: { label: 'Asistan', color: 'bg-green-100 text-green-800' }
  };

  const config = roleConfig[role as keyof typeof roleConfig] || { label: role, color: 'bg-gray-100 text-gray-800' };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

// Helper function to get status badge
const getStatusBadge = (status: string) => {
  const statusConfig = {
    pending: { label: 'Beklemede', color: 'bg-yellow-100 text-yellow-800' },
    approved: { label: 'Onaylandı', color: 'bg-green-100 text-green-800' },
    rejected: { label: 'Reddedildi', color: 'bg-red-100 text-red-800' }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || { label: status, color: 'bg-gray-100 text-gray-800' };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

export default function PendingUserDetailsModal({ isOpen, onClose, pendingUser, onUserUpdated }: PendingUserDetailsModalProps) {
  const { confirm, ConfirmModal } = useDentoConfirm();
  const { isAdmin } = useUser();

  if (!isOpen || !pendingUser) return null;

  // If user is not admin, don't render the modal
  if (!isAdmin) {
    return null;
  }

  const handleApproveUser = async () => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Onayla',
      message: `${pendingUser.firstName} ${pendingUser.lastName} kullanıcısını onaylamak istediğinizden emin misiniz? Onaylandıktan sonra kullanıcıya kayıt e-postası gönderilecektir.`,
      type: 'approve',
      confirmText: 'Onayla'
    });

    if (!confirmed) return;

    try {
      await pendingUserService.update(pendingUser.id, { status: 'approved' });
      toast.success(`${pendingUser.firstName} ${pendingUser.lastName} onaylandı ve kayıt e-postası gönderildi.`);
      onUserUpdated?.();
      onClose();
    } catch (error) {
      console.error('Error approving user:', error);
      toast.error('Kullanıcı onaylanırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const handleRejectUser = async () => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Reddet',
      message: `${pendingUser.firstName} ${pendingUser.lastName} kullanıcısının başvurusunu reddetmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      type: 'disapprove',
      confirmText: 'Reddet'
    });

    if (!confirmed) return;

    try {
      await pendingUserService.update(pendingUser.id, { status: 'rejected' });
      toast.success(`${pendingUser.firstName} ${pendingUser.lastName} kullanıcısının başvurusu reddedildi.`);
      onUserUpdated?.();
      onClose();
    } catch (error) {
      console.error('Error rejecting user:', error);
      toast.error('Kullanıcı reddedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  return (
    <>
      <ConfirmModal />
      <div className="fixed inset-0 z-50 overflow-y-auto">
        {/* Overlay */}
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={onClose} />
        
        {/* Modal */}
        <div className="flex min-h-full items-center justify-center p-4">
          <div className="relative w-full max-w-2xl bg-gray-50 rounded-2xl shadow-2xl transform transition-all animate-slideUp">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-t-2xl px-6 py-4 sticky top-0 z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-white">Bekleyen Kullanıcı Detayları</h2>
                    <p className="text-blue-100 text-sm">{pendingUser.firstName} {pendingUser.lastName}</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="w-9 h-9 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors cursor-pointer"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-5 max-h-[70vh] overflow-y-auto scrollbar-hide">
              {/* Status and Actions - Only show for admin users */}
              <div className="bg-white rounded-xl p-5 border border-gray-200/80 shadow-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white">
                    <Clock className="w-5 h-5" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800">Durum ve İşlemler</h3>
                  <div className="ml-auto">
                    {getStatusBadge(pendingUser.status)}
                  </div>
                </div>
                <div>
                  {pendingUser.status === 'pending' && (
                    <div className="flex space-x-3 mt-4">
                      <DentoButtonSecondary
                        onClick={handleApproveUser}
                        icon={<CheckCircle className="w-4 h-4" />}
                        bgColor="bg-green-600 hover:bg-green-700"
                        textColor="text-white"
                        className="flex-1"
                      >
                        Onayla
                      </DentoButtonSecondary>
                      <DentoButtonSecondary
                        onClick={handleRejectUser}
                        icon={<XCircle className="w-4 h-4" />}
                        bgColor="bg-red-600 hover:bg-red-700"
                        textColor="text-white"
                        className="flex-1"
                      >
                        Reddet
                      </DentoButtonSecondary>
                    </div>
                  )}
                </div>
              </div>

              {/* Basic Information */}
              <Section icon={<User className="w-5 h-5" />} title="Temel Bilgiler">
                <InfoItem label="Ad" value={pendingUser.firstName} />
                <InfoItem label="Soyad" value={pendingUser.lastName} />
                <InfoItem label="Rol" value={getRoleBadge(pendingUser.role)} />
                <InfoItem label="Klinik" value={pendingUser.clinic} />
              </Section>

              {/* Contact Information */}
              <Section icon={<Mail className="w-5 h-5" />} title="İletişim Bilgileri">
                <InfoItem label="E-posta" value={pendingUser.email} />
                <InfoItem label="Telefon" value={pendingUser.phone} />
                <InfoItem label="Adres" value={pendingUser.address || 'Adres bilgisi girilmemiş'} />
              </Section>

              {/* Personal Information */}
              <Section icon={<Calendar className="w-5 h-5" />} title="Kişisel Bilgiler">
                <InfoItem 
                  label="Doğum Tarihi" 
                  value={pendingUser.birthDate ? formatDateTime(pendingUser.birthDate) : 'Belirtilmemiş'} 
                />
              </Section>

              {/* System Information */}
              <Section icon={<Clock className="w-5 h-5" />} title="Sistem Bilgileri">
                <InfoItem label="Kullanıcı ID" value={<span className="font-mono">{pendingUser.id}</span>} />
                <InfoItem label="Başvuru Tarihi" value={formatDateTime(pendingUser.createdAt)} />
                <InfoItem label="Son Güncelleme" value={formatDateTime(pendingUser.updatedAt)} />
              </Section>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 