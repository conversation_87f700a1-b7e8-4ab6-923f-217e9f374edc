'use client';

import { X, User, Mail, Calendar, Shield, Crown, Trash2 } from 'lucide-react';
import { User as UserType } from '@/lib/types';
import { formatDateTime } from '@/lib/utils';
import { useDentoConfirm } from '@/lib/hooks/useDentoConfirm';
import { useUser } from '@/lib/hooks/useUser';
import { toast } from 'sonner';
import { adminService } from '@/lib/services/adminService';
import { userService } from '@/lib/services/userService';
import DentoButtonSecondary from '@/components/dentoui/DentoButtonSecondary';

const Section: React.FC<{ icon: React.ReactNode; title: string; children: React.ReactNode; className?: string }> = ({ icon, title, children, className }) => (
  <div className={`bg-white rounded-xl p-5 border border-gray-200/80 shadow-sm ${className}`}>
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
    </div>
    <div>
      {children}
    </div>
  </div>
);

const InfoItem: React.FC<{ label: string; value: React.ReactNode; }> = ({ label, value }) => (
  <div className="py-2 border-b border-gray-100 last:border-b-0">
    <p className="text-sm font-medium text-gray-600 mb-0.5">{label}</p>
    <div className="text-sm text-gray-800">{value}</div>
  </div>
);

export interface UserDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserType | null;
  onUserUpdated?: () => void;
}

// Helper function to get role badge
const getRoleBadge = (user: UserType) => {
  if (user.isAdmin) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
        Admin
      </span>
    );
  }

  const roleConfig = {
    doctor: { label: 'Doktor', color: 'bg-blue-100 text-blue-800' },
    assistant: { label: 'Asistan', color: 'bg-green-100 text-green-800' }
  };

  const config = roleConfig[user.role as keyof typeof roleConfig] || { label: user.role, color: 'bg-gray-100 text-gray-800' };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      {config.label}
    </span>
  );
};

export default function UserDetailsModal({ isOpen, onClose, user, onUserUpdated }: UserDetailsModalProps) {
  const { confirm, ConfirmModal } = useDentoConfirm();
  const { isAdmin } = useUser();

  if (!isOpen || !user) return null;

  // If user is not admin, don't render the modal
  if (!isAdmin) {
    return null;
  }

  const handleMakeAdmin = async () => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Admin Yap',
      message: `${user.firstName} ${user.lastName} kullanıcısını admin yapmak istediğinizden emin misiniz? Bu kullanıcı tüm admin yetkilerine sahip olacaktır.`,
      type: 'approve',
      confirmText: 'Admin Yap'
    });

    if (!confirmed) return;

    try {
      await adminService.setAdminClaim(user.id, true);
      toast.success(`${user.firstName} ${user.lastName} admin yapıldı.`);
      onUserUpdated?.();
      onClose();
    } catch (error) {
      console.error('Error making user admin:', error);
      toast.error('Kullanıcı admin yapılırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const handleRemoveAdmin = async () => {
    const confirmed = await confirm({
      title: 'Admin Yetkisini Kaldır',
      message: `${user.firstName} ${user.lastName} kullanıcısının admin yetkisini kaldırmak istediğinizden emin misiniz? Bu işlem geri alınabilir.`,
      type: 'disapprove',
      confirmText: 'Yetkiyi Kaldır'
    });

    if (!confirmed) return;

    try {
      await adminService.setAdminClaim(user.id, false);
      toast.success(`${user.firstName} ${user.lastName} kullanıcısının admin yetkisi kaldırıldı.`);
      onUserUpdated?.();
      onClose();
    } catch (error) {
      console.error('Error removing admin:', error);
      toast.error('Admin yetkisi kaldırılırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const handleDeleteUser = async () => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Sil',
      message: `${user.firstName} ${user.lastName} kullanıcısını kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve kullanıcının tüm verileri silinecektir.`,
      type: 'delete',
      confirmText: 'Sil'
    });

    if (!confirmed) return;

    try {
      await userService.delete(user.id);
      toast.success(`${user.firstName} ${user.lastName} kullanıcısı başarıyla silindi.`);
      onUserUpdated?.();
      onClose();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Kullanıcı silinirken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  return (
    <>
      <ConfirmModal />
      <div className="fixed inset-0 z-50 overflow-y-auto">
        {/* Overlay */}
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" onClick={onClose} />
        
        {/* Modal */}
        <div className="flex min-h-full items-center justify-center p-4">
          <div className="relative w-full max-w-2xl bg-gray-50 rounded-2xl shadow-2xl transform transition-all animate-slideUp">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-t-2xl px-6 py-4 sticky top-0 z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-white">Kullanıcı Detayları</h2>
                    <p className="text-blue-100 text-sm">{user.firstName} {user.lastName}</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="w-9 h-9 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center transition-colors cursor-pointer"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-5 max-h-[70vh] overflow-y-auto scrollbar-hide">
              {/* Role and Actions - Only show for admin users */}
              <div className="bg-white rounded-xl p-5 border border-gray-200/80 shadow-sm">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white">
                    <Crown className="w-5 h-5" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800">Rol ve Yetkiler</h3>
                  <div className="ml-auto">
                    {getRoleBadge(user)}
                  </div>
                </div>
                <div>
                  <div className="flex space-x-3 mt-4">
                    {user.isAdmin ? (
                      <DentoButtonSecondary
                        onClick={handleRemoveAdmin}
                        icon={<Crown className="w-4 h-4" />}
                        bgColor="bg-red-600 hover:bg-red-700"
                        textColor="text-white"
                        className="flex-1"
                      >
                        Admin Yetkisini Kaldır
                      </DentoButtonSecondary>
                    ) : (
                      <DentoButtonSecondary
                        onClick={handleMakeAdmin}
                        icon={<Crown className="w-4 h-4" />}
                        bgColor="bg-purple-600 hover:bg-purple-700"
                        textColor="text-white"
                        className="flex-1"
                      >
                        Admin Yap
                      </DentoButtonSecondary>
                    )}
                    <DentoButtonSecondary
                      onClick={handleDeleteUser}
                      icon={<Trash2 className="w-4 h-4" />}
                      bgColor="bg-red-600 hover:bg-red-700"
                      textColor="text-white"
                      className="flex-1"
                    >
                      Kullanıcıyı Sil
                    </DentoButtonSecondary>
                  </div>
                </div>
              </div>

              {/* Basic Information */}
              <Section icon={<User className="w-5 h-5" />} title="Temel Bilgiler">
                <InfoItem label="Ad" value={user.firstName} />
                <InfoItem label="Soyad" value={user.lastName} />
                <InfoItem label="Rol" value={getRoleBadge(user)} />
                <InfoItem label="Klinik" value={user.clinic} />
              </Section>

              {/* Contact Information */}
              <Section icon={<Mail className="w-5 h-5" />} title="İletişim Bilgileri">
                <InfoItem label="E-posta" value={user.email} />
                <InfoItem label="Telefon" value={user.phone} />
                <InfoItem label="Adres" value={user.address || 'Adres bilgisi girilmemiş'} />
              </Section>

              {/* Personal Information */}
              <Section icon={<Calendar className="w-5 h-5" />} title="Kişisel Bilgiler">
                <InfoItem 
                  label="Doğum Tarihi" 
                  value={user.birthDate ? formatDateTime(user.birthDate) : 'Belirtilmemiş'} 
                />
              </Section>

              {/* System Information */}
              <Section icon={<Shield className="w-5 h-5" />} title="Sistem Bilgileri">
                <InfoItem label="Kullanıcı ID" value={<span className="font-mono">{user.id}</span>} />
                <InfoItem label="Kayıt Tarihi" value={formatDateTime(user.createdAt)} />
                <InfoItem label="Son Güncelleme" value={formatDateTime(user.updatedAt)} />
              </Section>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}