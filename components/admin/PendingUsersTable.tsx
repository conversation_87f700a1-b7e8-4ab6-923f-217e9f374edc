'use client';

import { useState } from 'react';
import { User, Mail, Calendar, CheckCircle, XCircle, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { PendingUser } from '@/lib/types';
import { pendingUserService } from '@/lib/services/pendingUserService';
import { formatDateTime } from '@/lib/utils';
import { useDentoConfirm } from '@/lib/hooks/useDentoConfirm';
import { useUser } from '@/lib/hooks/useUser';
import PendingUserDetailsModal from './PendingUserDetailsModal';
import Pagination from '@/components/ui/Pagination';

interface PendingUsersTableProps {
  pendingUsers: PendingUser[];
  loading: boolean;
  searchQuery?: string;
  onSearchChange?: (value: string) => void;
  currentPage?: number;
  totalPages?: number;
  totalUsers?: number;
  itemsPerPage?: number;
  onPageChange?: (page: number) => void;
  onRefresh?: () => void;
}

// Helper function to get initials
const getInitials = (firstName: string, lastName: string) => {
  return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
};

export default function PendingUsersTable({
  pendingUsers,
  loading,
  searchQuery = '',
  onSearchChange,
  currentPage = 1,
  totalPages = 1,
  totalUsers = 0,
  itemsPerPage = 10,
  onPageChange,
  onRefresh
}: PendingUsersTableProps) {
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());
  const [selectedUser, setSelectedUser] = useState<PendingUser | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { confirm, ConfirmModal } = useDentoConfirm();
  const { isAdmin } = useUser();

  // If user is not admin, don't render the component
  if (!isAdmin) {
    return null;
  }

  // Approve user
  const handleApproveUser = async (pendingUser: PendingUser) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Onayla',
      message: `${pendingUser.firstName} ${pendingUser.lastName} kullanıcısını onaylamak istediğinizden emin misiniz? Onaylandıktan sonra kullanıcıya kayıt e-postası gönderilecektir.`,
      type: 'approve',
      confirmText: 'Onayla'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(pendingUser.id));
    try {
      await pendingUserService.update(pendingUser.id, { status: 'approved' });
      toast.success(`${pendingUser.firstName} ${pendingUser.lastName} onaylandı ve kayıt e-postası gönderildi.`);
      onRefresh?.();
    } catch (error) {
      console.error('Error approving user:', error);
      toast.error('Kullanıcı onaylanırken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(pendingUser.id);
        return newSet;
      });
    }
  };

  // Reject user
  const handleRejectUser = async (pendingUser: PendingUser) => {
    const confirmed = await confirm({
      title: 'Kullanıcıyı Reddet',
      message: `${pendingUser.firstName} ${pendingUser.lastName} kullanıcısının başvurusunu reddetmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      type: 'disapprove',
      confirmText: 'Reddet'
    });

    if (!confirmed) return;

    setProcessingIds(prev => new Set(prev).add(pendingUser.id));
    try {
      await pendingUserService.update(pendingUser.id, { status: 'rejected' });
      toast.success(`${pendingUser.firstName} ${pendingUser.lastName} kullanıcısının başvurusu reddedildi.`);
      onRefresh?.();
    } catch (error) {
      console.error('Error rejecting user:', error);
      toast.error('Kullanıcı reddedilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(pendingUser.id);
        return newSet;
      });
    }
  };

  // Handle user click to open modal
  const handleUserClick = (user: PendingUser) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  // Handle user updated callback
  const handleUserUpdated = () => {
    onRefresh?.();
  };

  return (
    <>
      <ConfirmModal />
      <PendingUserDetailsModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        pendingUser={selectedUser}
        onUserUpdated={handleUserUpdated}
      />
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {/* Table Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">
            Bekleyen Kullanıcılar ({totalUsers})
          </h2>
          {onSearchChange && (
            <div className="flex items-center space-x-4">
              {/* Search Input */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="block w-96 pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ad, e-posta veya telefon ile ara..."
                  value={searchQuery}
                  onChange={(e) => onSearchChange(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-blue-50">
              <tr>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span>Kullanıcı</span>
                  </div>
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2">
                    <Mail className="w-4 h-4" />
                    <span>İletişim</span>
                  </div>
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>Başvuru Tarihi</span>
                  </div>
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-blue-900 uppercase tracking-wider">
                  <div className="flex items-center space-x-2 justify-end">
                    <Clock className="w-4 h-4" />
                    <span>İşlemler</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={4} className="px-6 py-20 text-center">
                    <div className="flex items-center justify-center text-gray-500">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Yükleniyor...</span>
                    </div>
                  </td>
                </tr>
              ) : pendingUsers.length > 0 ? (
                pendingUsers.map((user) => (
                  <tr 
                    key={user.id} 
                    className="hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => handleUserClick(user)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">{getInitials(user.firstName, user.lastName)}</span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{user.email}</div>
                      <div className="text-sm text-gray-500">{user.phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDateTime(user.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end space-x-2">
                        {processingIds.has(user.id) ? (
                          <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApproveUser(user);
                              }}
                              className="p-2 text-green-600 rounded-full hover:bg-green-100 transition-colors cursor-pointer"
                              title="Kullanıcıyı Onayla"
                            >
                              <CheckCircle className="w-5 h-5" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRejectUser(user);
                              }}
                              className="p-2 text-red-600 rounded-full hover:bg-red-100 transition-colors cursor-pointer"
                              title="Kullanıcıyı Reddet"
                            >
                              <XCircle className="w-5 h-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-20 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Clock className="w-8 h-8 text-gray-400" />
                      </div>
                      <p className="text-lg font-medium text-gray-900 mb-2">Bekleyen Kullanıcı Yok</p>
                      <p className="text-gray-500">Şu anda onay bekleyen kullanıcı bulunmamaktadır.</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalUsers}
          itemsPerPage={itemsPerPage}
          onPageChange={onPageChange || (() => {})}
        />
      </div>
    </>
  );
}