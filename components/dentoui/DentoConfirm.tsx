'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>riangle, <PERSON><PERSON>ircle, XCircle, Trash2, User<PERSON><PERSON><PERSON>, UserX } from 'lucide-react';

export interface DentoConfirmProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'approve' | 'disapprove' | 'delete' | 'warning';
  isLoading?: boolean;
}

export default function DentoConfirm({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText = 'İptal',
  type = 'warning',
  isLoading = false
}: DentoConfirmProps) {
  if (!isOpen) return null;

  const getTypeConfig = () => {
    switch (type) {
      case 'approve':
        return {
          icon: <UserCheck className="w-8 h-8 text-white" />,
          gradient: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
          iconBg: 'bg-emerald-100',
          iconColor: 'text-emerald-600',
          confirmBg: 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700',
          defaultConfirmText: 'Onayla'
        };
      case 'disapprove':
        return {
          icon: <UserX className="w-8 h-8 text-white" />,
          gradient: 'bg-gradient-to-r from-red-500 to-red-600',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          confirmBg: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
          defaultConfirmText: 'Reddet'
        };
      case 'delete':
        return {
          icon: <Trash2 className="w-8 h-8 text-white" />,
          gradient: 'bg-gradient-to-r from-red-500 to-red-600',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          confirmBg: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
          defaultConfirmText: 'Sil'
        };
      default:
        return {
          icon: <AlertTriangle className="w-8 h-8 text-white" />,
          gradient: 'bg-gradient-to-r from-amber-500 to-amber-600',
          iconBg: 'bg-amber-100',
          iconColor: 'text-amber-600',
          confirmBg: 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700',
          defaultConfirmText: 'Onayla'
        };
    }
  };

  const config = getTypeConfig();
  const finalConfirmText = confirmText || config.defaultConfirmText;

  const handleConfirm = () => {
    if (!isLoading) {
      onConfirm();
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-[60] overflow-y-auto animate-fadeIn">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fadeIn" 
        onClick={handleClose} 
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl transform transition-all animate-slideUp">
          {/* Header */}
          <div className={`${config.gradient} rounded-t-2xl px-6 py-6`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                  {config.icon}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">
                    {title}
                  </h2>
                </div>
              </div>
              <button
                onClick={handleClose}
                disabled={isLoading}
                className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-xl flex items-center justify-center transition-colors cursor-pointer hover:scale-110 transform disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Icon and Message */}
            <div className="text-center mb-6">
              <div className={`w-16 h-16 ${config.iconBg} rounded-full flex items-center justify-center mx-auto mb-4`}>
                <div className={config.iconColor}>
                  {type === 'approve' && <CheckCircle className="w-8 h-8" />}
                  {type === 'disapprove' && <XCircle className="w-8 h-8" />}
                  {type === 'delete' && <Trash2 className="w-8 h-8" />}
                  {type === 'warning' && <AlertTriangle className="w-8 h-8" />}
                </div>
              </div>
              <p className="text-gray-700 text-base leading-relaxed">
                {message}
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-3">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="px-6 py-3 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-xl transition-colors font-semibold cursor-pointer hover:scale-105 transform disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {cancelText}
              </button>
              <button
                type="button"
                onClick={handleConfirm}
                disabled={isLoading}
                className={`group flex items-center space-x-2 px-6 py-3 ${config.confirmBg} text-white rounded-xl transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed min-w-[100px] justify-center`}
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <>
                    <span>{finalConfirmText}</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}