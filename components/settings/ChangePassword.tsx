import { useState } from 'react';
import { Lock, Eye, EyeOff, CheckCircle, AlertCircle, Shield } from 'lucide-react';
import DentoButtonPrimary from '../dentoui/DentoButtonPrimary';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { changePasswordSchema, type ChangePasswordInput } from '@/lib/schemas/validation';

interface ChangePasswordProps {
  onChangePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const ChangePassword = ({ onChangePassword }: ChangePasswordProps) => {
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ChangePasswordInput>({
    resolver: zodResolver(changePasswordSchema),
  });

  const [message, setMessage] = useState<{
    type: 'success' | 'error' | null;
    text: string;
  }>({ type: null, text: '' });

  const handlePasswordChange = async (data: ChangePasswordInput) => {
    setMessage({ type: null, text: '' });

    if (data.currentPassword === data.newPassword) {
      setMessage({
        type: 'error',
        text: 'Yeni şifre mevcut şifre ile aynı olamaz.'
      });
      return;
    }

    setIsChangingPassword(true);

    try {
      await onChangePassword(data.currentPassword, data.newPassword);
      setMessage({
        type: 'success',
        text: 'Şifreniz başarıyla değiştirildi.'
      });
      reset();
    } catch (error: unknown) {
      let errorMessage = 'Şifre değiştirme sırasında bir hata oluştu.';
      
      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };
        if (firebaseError.code === 'auth/wrong-password') {
          errorMessage = 'Mevcut şifreniz yanlış.';
        } else if (firebaseError.code === 'auth/weak-password') {
          errorMessage = 'Yeni şifre çok zayıf.';
        } else if (firebaseError.code === 'auth/requires-recent-login') {
          errorMessage = 'Güvenlik nedeniyle tekrar giriş yapmanız gerekiyor.';
        }
      }

      setMessage({
        type: 'error',
        text: errorMessage
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
        <Lock className="w-5 h-5 mr-2 text-blue-600" />
        Şifre Değiştir
      </h2>

      {message.type && (
        <div className={`mb-6 p-4 rounded-xl flex items-center ${
          message.type === 'success' 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-red-50 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
          ) : (
            <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
          )}
          <span className={`${
            message.type === 'success' ? 'text-green-800' : 'text-red-800'
          }`}>
            {message.text}
          </span>
        </div>
      )}

      <form onSubmit={handleSubmit(handlePasswordChange)} className="space-y-6">
        {/* Current Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Mevcut Şifre
          </label>
          <div className="relative">
            <input
              type={showCurrentPassword ? 'text' : 'password'}
              {...register('currentPassword')}
              className={`w-full px-4 py-3 border ${errors.currentPassword ? 'border-red-500' : 'border-gray-300'} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200 pr-12`}
              placeholder="Mevcut şifrenizi girin"
            />
            <button
              type="button"
              onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showCurrentPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
          {errors.currentPassword && (
            <p className="mt-2 text-sm text-red-600">{errors.currentPassword.message}</p>
          )}
        </div>

        {/* New Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Yeni Şifre
          </label>
          <div className="relative">
            <input
              type={showNewPassword ? 'text' : 'password'}
              {...register('newPassword')}
              className={`w-full px-4 py-3 border ${errors.newPassword ? 'border-red-500' : 'border-gray-300'} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200 pr-12`}
              placeholder="Yeni şifrenizi girin (en az 6 karakter)"
            />
            <button
              type="button"
              onClick={() => setShowNewPassword(!showNewPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showNewPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
          {errors.newPassword && (
            <p className="mt-2 text-sm text-red-600">{errors.newPassword.message}</p>
          )}
        </div>

        {/* Confirm New Password */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Yeni Şifre Tekrar
          </label>
          <div className="relative">
            <input
              type={showConfirmPassword ? 'text' : 'password'}
              {...register('confirmPassword')}
              className={`w-full px-4 py-3 border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200 pr-12`}
              placeholder="Yeni şifrenizi tekrar girin"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showConfirmPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="mt-2 text-sm text-red-600">{errors.confirmPassword.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <DentoButtonPrimary
          type="submit"
          disabled={isChangingPassword}
          icon={!isChangingPassword ? <Lock className="w-5 h-5" /> : undefined}
        >
          {isChangingPassword ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
              Şifre Değiştiriliyor...
            </>
          ) : (
            'Şifreyi Değiştir'
          )}
        </DentoButtonPrimary>
      </form>

      {/* Security Tips */}
      <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
        <h3 className="font-semibold text-blue-900 mb-2 flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Güvenlik İpuçları
        </h3>
        <ul className="text-sm text-blue-800 space-y-1 list-disc list-inside">
          <li>Şifre en az 8 karakter olmalıdır.</li>
          <li>Büyük harf (A-Z), küçük harf (a-z) ve rakam (0-9) içermelidir.</li>
          <li>Güvenliğiniz için şifrenizi kimseyle paylaşmayın.</li>
        </ul>
      </div>
    </div>
  );
};

export default ChangePassword; 