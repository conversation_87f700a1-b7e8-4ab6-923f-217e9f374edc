import { useState } from 'react';
import { User, Mail, Phone, MapPin, Calendar, Briefcase, Building, Edit } from 'lucide-react';
import { User as UserType } from '@/lib/types';
import { formatDateOnly } from '@/lib/utils';
import UpdateUserModal from './UpdateUserModal';
import DentoButtonSecondary from '../dentoui/DentoButtonSecondary';

interface UserInfoProps {
  userProfile: UserType | null;
  onUserUpdated?: () => void;
}

const UserInfo = ({ userProfile, onUserUpdated }: UserInfoProps) => {
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  // Helper function to format role
  const formatRole = (role: string) => {
    const roleMap = {
      admin: 'Yönetici',
      doctor: 'Doktor',
      assistant: 'Asistan'
    };
    return roleMap[role as keyof typeof roleMap] || role;
  };

  const handleUserUpdated = async () => {
    if (onUserUpdated) {
      onUserUpdated();
    }
  };

  return (
    <>
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <User className="w-5 h-5 mr-2 text-blue-600" />
            Kullanıcı Bilgileri
          </h2>
          {userProfile && (
            <DentoButtonSecondary
              onClick={() => setIsUpdateModalOpen(true)}
              icon={<Edit className="w-4 h-4" />}
            >
              Düzenle
            </DentoButtonSecondary>
          )}
        </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <User className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Ad</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile ? userProfile.firstName : 'Yükleniyor...'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <User className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Soyad</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile ? userProfile.lastName : 'Yükleniyor...'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Mail className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">E-posta</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile?.email || 'Yükleniyor...'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Phone className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Telefon</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile?.phone || 'Belirtilmemiş'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <MapPin className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Adres</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile?.address || 'Belirtilmemiş'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Doğum Tarihi</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {formatDateOnly(userProfile?.birthDate || null)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Briefcase className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Rol</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile ? formatRole(userProfile.role) : 'Yükleniyor...'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Building className="w-5 h-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Klinik</p>
              <p className="text-sm font-semibold text-blue-900 truncate">
                {userProfile?.clinic || 'Belirtilmemiş'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

      {/* Update User Modal */}
      {userProfile && (
        <UpdateUserModal
          isOpen={isUpdateModalOpen}
          onClose={() => setIsUpdateModalOpen(false)}
          userProfile={userProfile}
          onUserUpdated={handleUserUpdated}
        />
      )}
    </>
  );
};

export default UserInfo; 