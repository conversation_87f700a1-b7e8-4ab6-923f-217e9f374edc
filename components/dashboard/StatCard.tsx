'use client';

import { ReactNode } from 'react';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: ReactNode;
  color: string;
}

export default function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <div className={`group relative bg-gradient-to-br from-${color}-50 via-white to-${color}-50 rounded-2xl shadow-lg border border-${color}-100/50 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 bg-gradient-to-br from-${color}-500 to-${color}-600 rounded-xl shadow-lg`}>
          {icon}
        </div>
      </div>
      <div>
        <h3 className="text-gray-600 text-sm font-semibold mb-2 tracking-wide">{title}</h3>
        <p className="text-4xl font-bold text-gray-900">{value}</p>
      </div>
      <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br from-${color}-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
    </div>
  );
} 