'use client';

import { useState } from 'react';
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { pendingUserService } from '@/lib/services/pendingUserService';
import { userService } from '@/lib/services/userService';
import { PendingUser } from '@/lib/types';
import { registrationCompletionSchema, type RegistrationCompletionInput } from '@/lib/schemas/validation';

interface CompleteRegistrationProps {
  pendingUser: PendingUser;
  onComplete: () => void;
}

export default function CompleteRegistration({ pendingUser, onComplete }: CompleteRegistrationProps) {
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Partial<Record<keyof RegistrationCompletionInput, string>>>({});
  
  const { signUp } = useAuth();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    setError(null);
    
    // Clear validation error for this field when user starts typing
    if (validationErrors[name as keyof RegistrationCompletionInput]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setValidationErrors({});

    try {
      // Validate form data with Zod
      const validatedData = registrationCompletionSchema.parse(formData);
      
      setIsSubmitting(true);

      // Create Firebase Auth user
      await signUp(pendingUser.email, validatedData.password, `${pendingUser.firstName} ${pendingUser.lastName}`);
      
      // Create user profile in Firestore
      await userService.createFromPendingUser(pendingUser);
      
      // Update pending user status to approved
      await pendingUserService.update(pendingUser.id, { status: 'approved' });
      
      onComplete();
    } catch (error: unknown) {
      if (error instanceof Error && 'issues' in error) {
        // Zod validation errors
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        const fieldErrors: Partial<Record<keyof RegistrationCompletionInput, string>> = {};
        
        zodError.issues.forEach((issue) => {
          if (issue.path.length > 0) {
            fieldErrors[issue.path[0] as keyof RegistrationCompletionInput] = issue.message;
          }
        });
        
        setValidationErrors(fieldErrors);
        setError('Lütfen form hatalarını düzeltin.');
      } else {
        console.error('Registration completion error:', error);
        const firebaseError = error as { code?: string };
        if (firebaseError.code === 'auth/email-already-in-use') {
          setError('Bu e-posta adresi zaten kullanımda. Giriş yapmayı deneyin.');
        } else if (firebaseError.code === 'auth/weak-password') {
          setError('Şifre çok zayıf. Daha güçlü bir şifre seçin.');
        } else {
          setError('Kayıt tamamlanırken bir hata oluştu. Lütfen tekrar deneyin.');
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Kaydınız Onaylandı!
        </h1>
        <p className="text-gray-600 mb-4">
          Merhaba <strong>{pendingUser.firstName} {pendingUser.lastName}</strong>
        </p>
        <p className="text-gray-600">
          Hesabınızı aktif hale getirmek için bir şifre belirleyin.
        </p>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl flex items-start">
          <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Complete registration form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* User Info Display */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <h3 className="font-medium text-blue-900 mb-2">Hesap Bilgileriniz</h3>
          <div className="space-y-1 text-sm text-blue-700">
            <p><strong>E-posta:</strong> {pendingUser.email}</p>
            <p><strong>Telefon:</strong> {pendingUser.phone}</p>
          </div>
        </div>

        {/* Password field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
            Şifre*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Lock className="w-5 h-5 text-gray-400" />
            </div>
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              required
              value={formData.password}
              onChange={handleChange}
              className={`w-full pl-10 pr-12 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 ${
                validationErrors.password ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Şifrenizi girin"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <Eye className="w-5 h-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
          {validationErrors.password && (
            <p className="text-red-600 text-xs mt-1">{validationErrors.password}</p>
          )}
          {!validationErrors.password && (
            <p className="text-xs text-gray-500 mt-1">En az 8 karakter, büyük/küçük harf ve rakam içermelidir</p>
          )}
        </div>

        {/* Confirm password field */}
        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
            Şifre Tekrarı*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Lock className="w-5 h-5 text-gray-400" />
            </div>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              required
              value={formData.confirmPassword}
              onChange={handleChange}
              className={`w-full pl-10 pr-12 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 ${
                validationErrors.confirmPassword ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Şifrenizi tekrar girin"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showConfirmPassword ? (
                <EyeOff className="w-5 h-5 text-gray-400 hover:text-gray-600" />
              ) : (
                <Eye className="w-5 h-5 text-gray-400 hover:text-gray-600" />
              )}
            </button>
          </div>
          {validationErrors.confirmPassword && (
            <p className="text-red-600 text-xs mt-1">{validationErrors.confirmPassword}</p>
          )}
        </div>

        {/* Submit button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full py-3 px-4 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? 'Hesap Oluşturuluyor...' : 'Hesabımı Aktif Et'}
        </button>
      </form>
    </div>
  );
} 