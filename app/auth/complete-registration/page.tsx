'use client';

import { useState, useEffect, Suspense } from 'react';
import { Mail, ArrowLeft, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import AuthSlider from '@/components/auth/AuthSlider';
import CompleteRegistrationForm from '@/components/auth/CompleteRegistration';
import { useAuth } from '@/lib/contexts/AuthContext';
import Loading from '@/components/dentoui/Loading';
import { PendingUser } from '@/lib/types';
import { getFunctions, httpsCallable } from 'firebase/functions';

interface RegistrationTokenPayload {
  type: 'registration';
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address?: string;
  birthDate?: string;
  role: 'doctor' | 'assistant';
  clinic: string;
  iat?: number;
  exp?: number;
  iss?: string;
}

interface VerifyTokenResponse {
  success: boolean;
  payload?: RegistrationTokenPayload;
  error?: string;
}

function CompleteRegistrationContent() {
  const [pendingUser, setPendingUser] = useState<PendingUser | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const { user, loading: authLoading } = useAuth({
    redirectCondition: 'authenticated',
    redirectTo: '/dashboard',
  });

  useEffect(() => {
    const verifyToken = async () => {
      const token = searchParams.get('token');
      if (!token) {
        setError('Bu sayfa sadece özel kayıt bağlantısı ile erişilebilir. Lütfen yöneticinizden kayıt bağlantısını isteyin.');
        setIsLoading(false);
        return;
      }

      try {
        const functions = getFunctions();
        const verifyRegistrationToken = httpsCallable< { token: string }, VerifyTokenResponse >(functions, 'verifyRegistrationToken');
        const result = await verifyRegistrationToken({ token });
        const response = result.data;

        if (response.success && response.payload) {
          const tokenData = response.payload;
          const pendingUserData: PendingUser = {
            id: tokenData.userId,
            firstName: tokenData.firstName,
            lastName: tokenData.lastName,
            email: tokenData.email,
            phone: tokenData.phone || '',
            address: tokenData.address,
            birthDate: tokenData.birthDate ? new Date(tokenData.birthDate) : undefined,
            role: tokenData.role,
            clinic: tokenData.clinic,
            status: 'approved',
            createdAt: new Date(),
            updatedAt: new Date(),
          };
          setPendingUser(pendingUserData);
        } else {
            const errorMessage = response.error || 'Token verification failed';
            if (errorMessage === 'Token has expired') {
              setError('Kayıt bağlantısının süresi dolmuş. Lütfen yöneticinizden yeni bir bağlantı isteyin.');
            } else if (errorMessage === 'Invalid token signature') {
              setError('Geçersiz kayıt bağlantısı. Lütfen yöneticinizden yeni bir bağlantı isteyin.');
            } else {
              setError('Kayıt bağlantısı geçersiz veya bozuk. Lütfen yöneticinizden yeni bir bağlantı isteyin.');
            }
        }
      } catch (err) {
        console.error('Error verifying token:', err);
        setError('Kayıt bağlantısı doğrulanırken bir hata oluştu. Lütfen yöneticinizden yeni bir bağlantı isteyin.');
      } finally {
        setIsLoading(false);
      }
    };

    verifyToken();
  }, [searchParams]);

  const handleRegistrationComplete = () => {
    setIsCompleted(true);
    setTimeout(() => router.push('/auth/login'), 3000);
  };

  if (authLoading) {
    return <Loading />;
  }
  if (user) {
    return null; // Or redirect
  }

  const renderRightPanelContent = () => {
    if (isLoading) {
      return <Loading />;
    }

    if (isCompleted) {
      return (
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Kayıt Tamamlandı! 🎉
          </h1>
          <p className="text-gray-600 mb-6">
            Hesabınız başarıyla oluşturuldu. Giriş sayfasına yönlendiriliyorsunuz...
          </p>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-green-500 h-2 rounded-full animate-pulse" style={{ width: '100%' }}></div>
          </div>
        </div>
      );
    }
    
    if (error) {
      return (
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Erişim Hatası</h1>
            <p className="text-gray-600 text-sm leading-relaxed">{error}</p>
          </div>
          <div className="space-y-3">
            <Link href="/auth/signup" className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center">
              <Mail className="w-4 h-4 mr-2" />
              Yeni Kayıt Ol
            </Link>
            <Link href="/auth/login" className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Giriş Sayfasına Dön
            </Link>
          </div>
        </div>
      );
    }

    if (pendingUser) {
      return <CompleteRegistrationForm pendingUser={pendingUser} onComplete={handleRegistrationComplete} />;
    }

    return null; // Should not happen if logic is correct
  };

  return (
    <div className="min-h-screen flex bg-white p-8">
      {/* Left side - Visual content */}
      <div className="hidden lg:flex flex-1 relative overflow-hidden rounded-3xl mr-8 max-h-[calc(100vh-4rem)] max-w-[calc(50vw-2rem)]">
        <AuthSlider />
      </div>
      
      {/* Right side - Content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {renderRightPanelContent()}
        </div>
      </div>
    </div>
  );
}

export default function CompleteRegistration() {
  return (
    <Suspense fallback={<Loading />}>
      <CompleteRegistrationContent />
    </Suspense>
  );
} 