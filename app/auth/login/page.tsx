'use client';

// import Image from "next/image";
import { useState } from "react";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Mail, Lock, LogIn, Send } from "lucide-react";
import Link from "next/link";
import { getFirebaseAuthErrorTurkish } from '@/lib/utils';
import { RainbowButton } from "@/components/magicui/rainbow-button";
import { useAuth } from "@/lib/contexts/AuthContext";
import AuthSlider from "@/components/auth/AuthSlider";
import Loading from "@/components/dentoui/Loading";
import { setPersistence, browserLocalPersistence, browserSessionPersistence } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { loginSchema, type LoginFormInputs } from "@/lib/schemas/validation";

export default function Login() {
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [serverError, setServerError] = useState('');

  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormInputs>({
    resolver: zodResolver(loginSchema)
  });

  // useAuth now also handles redirect logic when options are provided
  const { user, loading: authLoading, signIn } = useAuth({
    redirectCondition: 'authenticated',
    redirectTo: '/dashboard',
  });

  const onSubmit = async (data: LoginFormInputs) => {
    setLoading(true);
    setServerError('');

    try {
      // Set persistence based on remember me selection
      const persistence = rememberMe ? browserLocalPersistence : browserSessionPersistence;
      await setPersistence(auth, persistence);

      await signIn(data.email, data.password);
    } catch (error) {
      console.error('Login error:', error);
      const firebaseError = error as { code?: string };
      setServerError(getFirebaseAuthErrorTurkish(firebaseError.code || ''));
    } finally {
      setLoading(false);
    }
  };

  // Show loading while checking auth status
  if (authLoading) {
    return <Loading />;
  }

  // Don't render login form if user is authenticated
  if (user) {
    return null;
  }

  return (
    <div className="min-h-screen flex bg-white p-8">
      {/* Left side - Visual content */}
      <div className="hidden lg:flex flex-1 relative overflow-hidden rounded-3xl mr-8 max-h-[calc(100vh-4rem)] max-w-[calc(50vw-2rem)]">
        <AuthSlider />
      </div>

      {/* Right side - Login form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          {/* Logo */}
          <div className="flex items-center mb-8">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
              <span className="text-white font-bold">🦷</span>
            </div>
            <span className="text-xl font-semibold text-gray-900">Diş Görüntüleme</span>
          </div>

          {/* Form header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Hesabınıza giriş yapın
            </h1>
            <p className="text-gray-600">
              Diş görüntüleme kontrol panelinize erişin
            </p>
          </div>

          {/* Error message */}
          {(serverError || errors.email || errors.password) && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
              <p className="text-red-600 text-sm">{serverError || errors.email?.message || errors.password?.message}</p>
            </div>
          )}

          {/* Login form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                E-posta*
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  type="email"
                  {...register("email")}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300"
                  placeholder="E-posta adresinizi girin"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Şifre*
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  type="password"
                  {...register("password")}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-500 placeholder:opacity-100 text-gray-900 hover:border-blue-300"
                  placeholder="••••••••"
                />
              </div>
            </div>

            {/* Remember me and forgot password */}
            <div className="flex items-center justify-between">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="ml-2 text-sm text-gray-700">Beni Hatırla</span>
              </label>

              <Link href="/auth/forgot-password" className="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors">
                Şifrenizi mi unuttunuz?
              </Link>
            </div>

            <RainbowButton
              className="w-full py-6 rounded-xl font-medium flex items-center justify-center gap-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Giriş yapılıyor...
                </>
              ) : (
                <>
                  <LogIn className="w-5 h-5" />
                  Giriş Yap
                </>
              )}
            </RainbowButton>
          </form>

          {/* Bottom links */}
          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">
              Hesabınız yok mu?
            </p>
            <Link href="/auth/signup" className="w-full bg-gradient-to-r from-blue-50 to-blue-100 text-blue-800 py-3.5 rounded-xl font-medium hover:from-blue-100 hover:to-blue-200 transition-all duration-200 flex items-center justify-center gap-2 shadow-md hover:shadow-lg transform hover:scale-[1.02] cursor-pointer border border-blue-200">
              <Send className="w-5 h-5" />
              Hesap Oluşturun
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 