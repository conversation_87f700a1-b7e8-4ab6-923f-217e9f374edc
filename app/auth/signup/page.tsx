'use client';

import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Mail, User, Send, ArrowLeft, CheckCircle, MapPin, Briefcase, Building } from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { tr as dateFnsTr } from "date-fns/locale";
import { Calendar as CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { RainbowButton } from "@/components/magicui/rainbow-button";
import { useAuth } from "@/lib/contexts/AuthContext";
import Loading from "@/components/dentoui/Loading";
import { pendingUserService } from "@/lib/services/pendingUserService";
import { pendingUserSchema, type PendingUserInput } from "@/lib/schemas/validation";
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import tr from 'react-phone-input-2/lang/tr.json';

export default function ContactUs() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [serverError, setServerError] = useState('');

  const { register, handleSubmit, control, formState: { errors } } = useForm<PendingUserInput>({
    resolver: zodResolver(pendingUserSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: '',
      birthDate: undefined,
      role: 'doctor',
      clinic: ''
    }
  });
  
  // useAuth now also handles redirect logic when options are provided
  const { user, loading: authLoading } = useAuth({
    redirectCondition: 'authenticated',
    redirectTo: '/dashboard',
  });

  const onSubmit = async (data: PendingUserInput) => {
    setIsSubmitting(true);
    setServerError('');
    
    try {
      // Save user to pending users collection
      await pendingUserService.create(data);
      
      setIsSubmitted(true);
      console.log('User successfully added to pending users:', data);
    } catch (error) {
      console.error('Error submitting form:', error);
      setServerError('Kayıt talebi gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading while checking auth status
  if (authLoading) {
    return <Loading />;
  }

  // Don't render contact form if user is authenticated
  if (user) {
    return null;
  }

  return (
    <div className="min-h-screen flex bg-white p-8">
      {/* Contact form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="w-full max-w-2xl">
          {/* Logo */}
          <div className="flex items-center mb-6">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
              <span className="text-white font-bold">🦷</span>
            </div>
            <span className="text-xl font-semibold text-gray-900">Diş Görüntüleme</span>
          </div>

          {/* Back to login link */}
          <Link href="/auth/login" className="inline-flex items-center text-gray-600 hover:text-blue-600 mb-6 transition-colors">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Giriş sayfasına dön
          </Link>

          {/* Form header */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Platform Kayıt Talebi
            </h1>
            <p className="text-gray-600">
              Diş Görüntüleme platformuna katılmak için başvurunuzu gönderin
            </p>
          </div>

          {/* Success message */}
          {isSubmitted ? (
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Kayıt Talebiniz Alındı!
                </h2>
                <p className="text-gray-600 mb-4">
                  Platform kayıt talebiniz başarıyla gönderildi. Yönetici onayından sonra e-posta adresinize kayıt tamamlama bağlantısı gönderilecektir.
                </p>
                <p className="text-sm text-gray-500">
                  Onay süreci tamamlandığında e-posta adresinize bilgilendirme gönderilecektir.
                </p>
              </div>

            </div>
          ) : (
            <>
              {/* Error message */}
              {(serverError || errors.firstName || errors.lastName || errors.email || errors.phone || errors.role || errors.clinic || errors.address || errors.birthDate) && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-red-700 text-sm">
                    {serverError || 
                     errors.firstName?.message || 
                     errors.lastName?.message || 
                     errors.email?.message || 
                     errors.phone?.message || 
                     errors.role?.message || 
                     errors.clinic?.message || 
                     errors.address?.message || 
                     errors.birthDate?.message}
                  </p>
                </div>
              )}

              {/* Contact form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4" noValidate>
                {/* Row 1: Name fields */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                      Ad*
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        id="firstName"
                        type="text"
                        {...register("firstName")}
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                          errors.firstName ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Adınız"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                      Soyad*
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        id="lastName"
                        type="text"
                        {...register("lastName")}
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                          errors.lastName ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Soyadınız"
                      />
                    </div>
                  </div>
                </div>

                {/* Row 2: Email and Phone */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      E-posta*
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        id="email"
                        type="email"
                        {...register("email")}
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                          errors.email ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="E-posta adresinizi girin"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      Telefon Numarası*
                    </label>
                    <div className="relative">
                      <Controller
                        name="phone"
                        control={control}
                        render={({ field }) => (
                          <PhoneInput
                            country={'tr'}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Telefon numarası giriniz"
                            disableDropdown={true}
                            countryCodeEditable={false}
                            inputProps={{
                              name: 'phone',
                              required: true,
                              className: `w-full px-4 py-3 pl-14 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-text hover:border-blue-400 ${
                                errors.phone 
                                  ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                                  : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                              } bg-white shadow-sm`
                            }}
                            containerClass="w-full"
                            buttonClass={`border rounded-l-xl hover:border-blue-400 transition-all bg-white px-3 shadow-sm ${
                              errors.phone 
                                ? 'border-red-300 hover:border-red-300' 
                                : 'border-gray-300 hover:border-blue-400'
                            }`}
                            specialLabel=""
                            localization={tr}
                          />
                        )}
                      />
                    </div>
                  </div>
                </div>

                {/* Row 3: Address and Birth Date */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                      Adres
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <MapPin className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        id="address"
                        type="text"
                        {...register("address")}
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                          errors.address ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Adresinizi girin (opsiyonel)"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Doğum Tarihi
                    </label>
                    <div className="relative">
                      <Controller
                        name="birthDate"
                        control={control}
                        render={({ field }) => (
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="ghost"
                                className={cn(
                                  "w-full h-12 px-4 py-3 pl-12 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-pointer hover:border-blue-400 justify-start text-left font-normal",
                                  !field.value && "text-gray-500",
                                  errors.birthDate 
                                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',
                                  "bg-white shadow-sm hover:bg-white"
                                )}
                              >
                                <CalendarIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                                <span className="ml-8 text-base">
                                  {field.value ? (
                                    format(field.value, "dd/MM/yyyy", { locale: dateFnsTr })
                                  ) : (
                                    "Doğum tarihi seçiniz (opsiyonel)"
                                  )}
                                </span>
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date > new Date() || date < new Date("1900-01-01")
                                }
                                captionLayout="dropdown"
                                locale={dateFnsTr}
                                formatters={{
                                  formatMonthDropdown: (date) => 
                                    date.toLocaleDateString('tr-TR', { month: 'long' }),
                                  formatYearDropdown: (date) => 
                                    date.getFullYear().toString(),
                                }}
                                autoFocus
                              />
                            </PopoverContent>
                          </Popover>
                        )}
                      />
                    </div>
                  </div>
                </div>

                {/* Row 4: Role and Clinic */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
                      Rol*
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <Briefcase className="w-5 h-5 text-gray-400" />
                      </div>
                      <Controller
                        name="role"
                        control={control}
                        render={({ field }) => (
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger
                              className={cn(
                                "w-full pl-10 pr-4 py-6 border rounded-xl focus:outline-none focus:ring-2 transition-all cursor-pointer hover:border-blue-400",
                                errors.role
                                  ? "border-red-300 focus:ring-red-500 focus:border-red-500"
                                  : "border-gray-300 focus:ring-blue-500 focus:border-blue-500",
                                "bg-white shadow-sm"
                              )}
                            >
                              <SelectValue placeholder="Rol seçiniz" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="doctor">Doktor</SelectItem>
                              <SelectItem value="assistant">Asistan</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="clinic" className="block text-sm font-medium text-gray-700 mb-2">
                      Klinik Adı*
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Building className="w-5 h-5 text-gray-400" />
                      </div>
                      <input
                        id="clinic"
                        type="text"
                        {...register("clinic")}
                        className={`w-full pl-10 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-all duration-200 placeholder:text-gray-400 text-gray-900 hover:border-blue-300 ${
                          errors.clinic ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="Klinik adınızı girin"
                      />
                    </div>
                  </div>
                </div>

            

                <RainbowButton 
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full py-6 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Gönderiliyor...
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      Kayıt Talebini Gönder
                    </>
                  )}
                </RainbowButton>
              </form>
            </>
          )}

          {/* Bottom link */}
          {!isSubmitted && (
            <div className="mt-8 text-center">
              <p className="text-gray-600">
                Hesabınız var mı?{' '}
                <Link href="/auth/login" className="text-blue-600 hover:text-blue-700 font-medium transition-colors">
                  Giriş yapın
                </Link>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 