'use client';

import { useEffect, useState } from 'react';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import AddPatientModal from '@/components/hastalar/AddPatientModal';
import Loading from '@/components/dentoui/Loading';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Patient } from '@/lib/types';
import Header from '@/components/dentoui/Header';
import DentoButtonSecondary from '@/components/dentoui/DentoButtonSecondary';
import { Plus } from 'lucide-react';
import { addPatient, getPatientsPaginated, getAllPatientsPaginated } from '@/lib/services/patientService';
import { PatientFormData } from '@/lib/schemas/validation';
import StatsCards from '@/components/hastalar/StatsCards';
import PatientsTable from '@/components/hastalar/PatientsTable';
import { useCallback } from 'react';
import { useDebounce } from '@/lib/hooks/useDebounce';
import { useUser } from '@/lib/hooks/useUser';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { User } from '@/lib/types';



export default function Hastalar() {
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [isLoadingPatients, setIsLoadingPatients] = useState(true);
  const [totalPatients, setTotalPatients] = useState(0);
  const [users, setUsers] = useState<User[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;


  const { user, loading: authLoading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const { isAdmin } = useUser();

  // Reset to page 1 whenever search query changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
    // We only want to run this when the search query changes, not when the page changes.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchQuery]);

  const totalPages = Math.ceil(totalPatients / ITEMS_PER_PAGE);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const fetchPatients = useCallback(async () => {
    if (!user) return;
    try {
      setIsLoadingPatients(true);

      if (isAdmin) {
        // Admin fetches all patients across all doctors and all users
        const [patientsData, usersSnapshot] = await Promise.all([
          getAllPatientsPaginated({
            page: currentPage,
            pageSize: ITEMS_PER_PAGE,
            searchQuery: debouncedSearchQuery,
          }),
          getDocs(collection(db, 'users'))
        ]);

        const { patients: fetchedPatients, total: totalFetched } = patientsData;


        const fetchedUsers = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          birthDate: doc.data().birthDate?.toDate() || new Date(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date()
        })) as User[];

        setPatients(fetchedPatients);
        setUsers(fetchedUsers);
        setTotalPatients(totalFetched);
      } else {
        // Regular user fetches only their own patients
        const { patients: fetched, total } = await getPatientsPaginated({
          doctorId: user.uid,
          page: currentPage,
          pageSize: ITEMS_PER_PAGE,
          searchQuery: debouncedSearchQuery,
        });
        setPatients(fetched);
        setTotalPatients(total);
      }
    } catch (error) {
      console.error('Failed to fetch patients:', error);
    } finally {
      setIsLoadingPatients(false);
    }
  }, [user, currentPage, debouncedSearchQuery, isAdmin]);

  useEffect(() => {
    if (!authLoading) {
      fetchPatients();
    }
  }, [user, authLoading, fetchPatients]);

  if (authLoading) {
    return <Loading message="Kimlik doğrulanıyor..." />;
  }
  
  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard' },
    { label: 'Hastalar', href: '/dashboard/hastalar', isActive: true },
  ];

  const headerActions = (
    <DentoButtonSecondary
      onClick={() => setIsAddPatientModalOpen(true)}
      icon={<Plus className="w-5 h-5" />}
      bgColor="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
      textColor="text-white"
      iconAnimation="group-hover:rotate-90"
      className="shadow-lg hover:shadow-xl"
    >
      Yeni Hasta Ekle
    </DentoButtonSecondary>
  );

  const handleAddPatient = async (patientData: PatientFormData) => {
    if (!user) {
      console.error('User not authenticated');
      // Optionally, show a toast or error message to the user
      return;
    }

    if (!patientData.birthDate) {
      console.error('Birth date is missing');
      // Optionally, show a toast or error message to the user
      return;
    }

    try {
      const patientPayload: Omit<Patient, 'id' | 'createdAt' | 'updatedAt'> = {
        ...patientData,
        birthDate: patientData.birthDate,
        doctorId: user.uid,
      };

      const newPatientId = await addPatient(patientPayload);
      
      const newPatient: Patient = {
        ...patientPayload,
        id: newPatientId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setPatients(prev => [...prev, newPatient]);
      setIsAddPatientModalOpen(false); // Close modal on success
    } catch (error) {
      console.error('Failed to add patient:', error);
      // Optionally, show an error message to the user
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 overflow-y-auto ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="Hastalar"
          description="Tüm hastaları görüntüleyin ve yönetin"
          breadcrumbs={breadcrumbs}
          rightComponent={headerActions}
        />

        {/* Main Content Area */}
        <div className="p-8">
          {/* Statistics Cards */}
          <StatsCards patients={patients} totalPatients={totalPatients} />

          {/* Patient Table */}
          <PatientsTable
            patients={patients}
            users={users}
            isLoading={isLoadingPatients}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            onRefresh={fetchPatients}
            currentPage={currentPage}
            totalPages={totalPages}
            totalPatients={totalPatients}
            itemsPerPage={ITEMS_PER_PAGE}
            onPageChange={handlePageChange}
          />

        </div>

        {/* Add Patient Modal */}
        <AddPatientModal
          isOpen={isAddPatientModalOpen}
          onClose={() => setIsAddPatientModalOpen(false)}
          onSubmit={handleAddPatient}
        />
      </div>
    </div>
  );
} 