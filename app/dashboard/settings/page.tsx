'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useUser } from '@/lib/hooks/useUser';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Header from '@/components/dentoui/Header';
import Loading from '@/components/dentoui/Loading';
import UserInfo from '@/components/settings/UserInfo';
import ChangePassword from '@/components/settings/ChangePassword';
import DentoTabs from '@/components/dentoui/DentoTabs';
import { User, Settings } from 'lucide-react';

export default function SettingsPage() {
  const { changePassword } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const { userProfile, loading: userLoading, refetchUserProfile } = useUser();
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [activeTab, setActiveTab] = useState('user-info');

  const settingsTabs = [
    {
      id: 'user-info',
      label: 'Kullanıcı Bilgileri',
      icon: <User className="w-5 h-5" />
    },
    {
      id: 'others',
      label: 'Diğer Ayarlar',
      icon: <Settings className="w-5 h-5" />
    }
  ] as const;

  if (userLoading) {
    return <Loading message="Ayarlar yükleniyor..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className={`transition-all duration-300 ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="Ayarlar"
          description="Hesap ayarlarınızı yönetin ve güvenliğinizi koruyun."
          breadcrumbs={[
            { label: 'Anasayfa', href: '/dashboard' },
            { label: 'Ayarlar', isActive: true }
          ]}
        />

        <div className="p-8">
          <DentoTabs 
            tabs={settingsTabs}
            activeTab={activeTab} 
            onTabChange={(id) => setActiveTab(id)} 
          />

          {activeTab === 'user-info' ? (
            <div className="grid grid-cols-1 xl:grid-cols-12 gap-8">
              {/* User Info Card */}
              <div className="xl:col-span-7">
                <UserInfo userProfile={userProfile} onUserUpdated={refetchUserProfile} />
              </div>

              {/* Password Change Card */}
              <div className="xl:col-span-5">
                <ChangePassword onChangePassword={changePassword} />
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-12 text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Diğer Ayarlar Yakında</h3>
              <p className="text-gray-600">Bu bölümdeki ayarlar yakında kullanıma sunulacaktır.</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
} 