'use client';

import { useAuth } from '@/lib/contexts/AuthContext';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import { useUserStore } from '@/lib/stores/userStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Loading from '@/components/dentoui/Loading';
import Header from '@/components/dentoui/Header';
import {
  getIstemFormuCounts,
  getTotalPatients,
  getTotalScans,
} from '@/lib/services/dashboardService';
import { useEffect, useState } from 'react';
import StatCard from '@/components/dashboard/StatCard';
import DashboardCharts from '@/components/dashboard/DashboardCharts';

interface DashboardStats {
  totalPatients: number;
  totalScans: number;
  waitingIstemFormu: number;
  completedIstemFormu: number;
}

export default function Dashboard() {
  // useAuth returns authentication helpers and handles redirect when options supplied
  const { user, loading } = useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const [stats, setStats] = useState<DashboardStats>({
    totalPatients: 0,
    totalScans: 0,
    waitingIstemFormu: 0,
    completedIstemFormu: 0,
  });
  const [loadingStats, setLoadingStats] = useState(true);

  const { userProfile, isAdmin } = useUserStore();
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;

  useEffect(() => {
    if (user) {
      const fetchStats = async () => {
        try {
          setLoadingStats(true);
          const [totalPatients, totalScans, istemFormuCounts] =
            await Promise.all([
              getTotalPatients(user.uid),
              getTotalScans(user.uid),
              getIstemFormuCounts(user.uid),
            ]);
          setStats({
            totalPatients,
            totalScans,
            waitingIstemFormu: istemFormuCounts.waiting,
            completedIstemFormu: istemFormuCounts.completed,
          });
        } catch (error) {
          console.error('Error fetching dashboard stats:', error);
        } finally {
          setLoadingStats(false);
        }
      };

      fetchStats();
    }
  }, [user]);

  const breadcrumbs = [
    { label: 'Anasayfa', href: '/dashboard', isActive: true },
  ];

  const title = (
    <div className="flex items-center gap-2">
      <span>Tekrar hoş geldiniz,</span>
      <span className="text-blue-600 font-semibold">
        {userProfile?.firstName || user?.email}
      </span>
      {isAdmin && (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Admin
        </span>
      )}
    </div>
  );

  const currentDate = new Date().toLocaleDateString('tr-TR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  if (loading) {
    return <Loading message="Dashboard yükleniyor..." />;
  }

  const statCards = [
    {
      title: 'Toplam Hasta',
      value: stats.totalPatients,
      icon: (
        <svg
          className="w-7 h-7 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
          />
        </svg>
      ),
      color: 'blue',
    },
    {
      title: 'Toplam Taramalar',
      value: stats.totalScans,
      icon: (
        <svg
          className="w-7 h-7 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>
      ),
      color: 'yellow',
    },
    {
      title: 'Bekleyen Formlar',
      value: stats.waitingIstemFormu,
      icon: (
        <svg
          className="w-7 h-7 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      ),
      color: 'orange',
    },
    {
      title: 'Tamamlanan Formlar',
      value: stats.completedIstemFormu,
      icon: (
        <svg
          className="w-7 h-7 text-white"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
      color: 'emerald',
    },
  ];

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div
        className={`flex-1 flex flex-col transition-all duration-300 ${
          isSidebarExpanded ? 'ml-64' : 'ml-20'
        }`}
      >
        <Header
          title={title}
          breadcrumbs={breadcrumbs}
          description={currentDate}
        />

        <main className="flex-1 p-6 bg-gray-50 overflow-y-auto">
          {/* Stat Cards */}
          {loadingStats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="bg-white p-6 rounded-2xl shadow-lg border animate-pulse"
                >
                  <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-10 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {statCards.map((card, index) => (
                <StatCard
                  key={index}
                  title={card.title}
                  value={card.value}
                  icon={card.icon}
                  color={card.color}
                />
              ))}
            </div>
          )}

          {/* Charts Section */}
          <DashboardCharts />
        </main>
      </div>
    </div>
  );
} 