'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useUser } from '@/lib/hooks/useUser';
import { useSidebarStore } from '@/lib/stores/sidebarStore';
import Sidebar from '@/components/dentoui/Sidebar';
import Header from '@/components/dentoui/Header';
import Loading from '@/components/dentoui/Loading';
import PendingUsersTable from '@/components/admin/PendingUsersTable';
import AllUsersTable from '@/components/admin/AllUsersTable';
import DentoTabs from '@/components/dentoui/DentoTabs';
import { Users, Clock } from 'lucide-react';
import { useDebounce } from '@/lib/hooks/useDebounce';
import { pendingUserService } from '@/lib/services/pendingUserService';
import { userService } from '@/lib/services/userService';
import { PendingUser, User } from '@/lib/types';

export default function AdminDashboard() {
  useAuth({
    redirectCondition: 'unauthenticated',
    redirectTo: '/auth/login',
  });

  const { loading: userLoading, isAdmin } = useUser();
  const { isCollapsed, isHovered } = useSidebarStore();
  const isSidebarExpanded = !isCollapsed || isHovered;
  const [activeTab, setActiveTab] = useState('pending-users');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const ITEMS_PER_PAGE = 10;

  // Data state
  const [pendingUsers, setPendingUsers] = useState<PendingUser[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const adminTabs = [
    {
      id: 'pending-users',
      label: 'Bekleyen Kullanıcılar',
      icon: <Clock className="w-5 h-5" />
    },
    {
      id: 'all-users',
      label: 'Tüm Kullanıcılar',
      icon: <Users className="w-5 h-5" />
    }
  ] as const;

  // Reset to page 1 whenever search query changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery, currentPage]);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
  };

  const fetchPendingUsers = useCallback(async () => {
    if (activeTab === 'pending-users') {
      try {
        setLoading(true);
        const { users, total } = await pendingUserService.getPaginated({
          page: currentPage,
          pageSize: ITEMS_PER_PAGE,
          searchQuery: debouncedSearchQuery,
          status: 'pending'
        });
        setPendingUsers(users);
        setTotalItems(total);
        setTotalPages(Math.ceil(total / ITEMS_PER_PAGE));
      } catch (error) {
        console.error('Error fetching pending users:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [activeTab, currentPage, debouncedSearchQuery]);

  const fetchAllUsers = useCallback(async () => {
    if (activeTab === 'all-users') {
      try {
        setLoading(true);
        const { users, total } = await userService.getPaginated({
          page: currentPage,
          pageSize: ITEMS_PER_PAGE,
          searchQuery: debouncedSearchQuery
        });
        setAllUsers(users);
        setTotalItems(total);
        setTotalPages(Math.ceil(total / ITEMS_PER_PAGE));
      } catch (error) {
        console.error('Error fetching all users:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [activeTab, currentPage, debouncedSearchQuery]);

  // Fetch data when tab, page, or search changes
  useEffect(() => {
    if (activeTab === 'pending-users') {
      fetchPendingUsers();
    } else if (activeTab === 'all-users') {
      fetchAllUsers();
    }
  }, [activeTab, currentPage, debouncedSearchQuery, fetchPendingUsers, fetchAllUsers]);

  if (userLoading) {
    return <Loading message="Yetkilendirme kontrol ediliyor..." />;
  }

  // Redirect non-admin users
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 text-center max-w-md">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Yetkisiz Erişim</h1>
          <p className="text-gray-600 mb-6">Bu sayfaya erişim yetkiniz bulunmamaktadır.</p>
          <a 
            href="/dashboard" 
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Ana Sayfaya Dön
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      
      <main className={`transition-all duration-300 ${
        isSidebarExpanded ? 'ml-64' : 'ml-16'
      }`}>
        <Header
          title="Yönetici Paneli"
          description="Kullanıcıları yönetin ve sistem ayarlarını yapılandırın."
          breadcrumbs={[
            { label: 'Anasayfa', href: '/dashboard' },
            { label: 'Yönetici Paneli', isActive: true }
          ]}
        />

        <div className="p-8">
          <DentoTabs 
            tabs={adminTabs}
            activeTab={activeTab}
            onTabChange={(id) => {
              setActiveTab(id);
              setCurrentPage(1); // Reset to first page when switching tabs
              setSearchQuery(''); // Reset search when switching tabs
            }}
          />

          {activeTab === 'pending-users' && (
            <PendingUsersTable
              pendingUsers={pendingUsers}
              loading={loading}
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              currentPage={currentPage}
              totalPages={totalPages}
              totalUsers={totalItems}
              itemsPerPage={ITEMS_PER_PAGE}
              onPageChange={handlePageChange}
              onRefresh={fetchPendingUsers}
            />
          )}
          {activeTab === 'all-users' && (
            <AllUsersTable
              users={allUsers}
              loading={loading}
              searchQuery={searchQuery}
              onSearchChange={handleSearchChange}
              currentPage={currentPage}
              totalPages={totalPages}
              totalUsers={totalItems}
              itemsPerPage={ITEMS_PER_PAGE}
              onPageChange={handlePageChange}
              onRefresh={fetchAllUsers}
            />
          )}
        </div>
      </main>
    </div>
  );
} 