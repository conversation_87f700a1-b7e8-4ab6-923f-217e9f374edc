import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { DocumentData, Timestamp } from "firebase/firestore";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Date formatting utilities
export const formatDate = (date: Date | undefined | null): string => {
  if (!date) return '-';
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

export const formatDateTime = (date: Date | undefined | null): string => {
  if (!date) return '-';
  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

export const formatDateOnly = (date: Date | undefined | null): string => {
  if (!date) return 'Belirtilmemiş';
  return new Intl.DateTimeFormat('tr-TR').format(date);
};

// Helper function to convert Firestore timestamps to Date objects
export const convertTimestamps = (data: DocumentData) => {
  const converted = { ...data };
  Object.keys(converted).forEach(key => {
    if (converted[key] instanceof Timestamp) {
      converted[key] = converted[key].toDate();
    }
  });
  return converted;
};

// Firebase Auth Error Handler
export const getFirebaseAuthErrorTurkish = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/invalid-email':
      return 'Geçersiz e-posta adresi. Lütfen kontrol edip tekrar deneyin.';
    case 'auth/user-disabled':
      return 'Bu kullanıcı hesabı devre dışı bırakılmıştır.';
    case 'auth/user-not-found':
      return 'Bu e-posta adresi ile kayıtlı bir kullanıcı bulunamadı.';
    case 'auth/wrong-password':
      return 'Hatalı şifre. Lütfen şifrenizi kontrol edip tekrar deneyin.';
    case 'auth/invalid-credential':
        return 'Hatalı e-posta veya şifre. Lütfen bilgilerinizi kontrol edip tekrar deneyin.';
    case 'auth/email-already-in-use':
      return 'Bu e-posta adresi zaten başka bir hesap tarafından kullanılıyor.';
    case 'auth/operation-not-allowed':
      return 'Bu işlem şu anda etkin değil. Lütfen daha sonra tekrar deneyin.';
    case 'auth/weak-password':
      return 'Şifre çok zayıf. Lütfen daha güçlü bir şifre seçin.';
    default:
      return 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
  }
};
