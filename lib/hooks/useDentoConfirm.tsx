'use client';

import React from 'react';
import { useState, useCallback } from 'react';
import DentoConfirm from '../../components/dentoui/DentoConfirm';

interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'approve' | 'disapprove' | 'delete' | 'warning';
}

interface UseDentoConfirmReturn {
  confirm: (options: ConfirmOptions) => Promise<boolean>;
  ConfirmModal: () => React.ReactNode;
}

export function useDentoConfirm(): UseDentoConfirmReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<ConfirmOptions>({
    title: '',
    message: ''
  });
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null);

  const confirm = useCallback((confirmOptions: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setOptions(confirmOptions);
      setIsOpen(true);
      setResolvePromise(() => resolve);
    });
  }, []);

  const handleConfirm = useCallback(() => {
    setIsOpen(false);
    if (resolvePromise) {
      resolvePromise(true);
      setResolvePromise(null);
    }
  }, [resolvePromise]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    if (resolvePromise) {
      resolvePromise(false);
      setResolvePromise(null);
    }
  }, [resolvePromise]);

  const ConfirmModal = useCallback(() => {
    if (!isOpen) return null;

    return (
      <DentoConfirm
        isOpen={isOpen}
        onClose={handleClose}
        onConfirm={handleConfirm}
        title={options.title}
        message={options.message}
        confirmText={options.confirmText}
        cancelText={options.cancelText}
        type={options.type}
        isLoading={false}
      />
    );
  }, [isOpen, handleClose, handleConfirm, options]);

  return {
    confirm,
    ConfirmModal
  };
}