import { db } from '../firebase';
import {
  collection,
  query,
  where,
  getCountFromServer,
} from 'firebase/firestore';

const patientsCollection = collection(db, 'patients');
const patientImagesCollection = collection(db, 'patient-images');

export const getTotalPatients = async (doctorId: string): Promise<number> => {
  try {
    const q = query(patientsCollection, where('doctorId', '==', doctorId));
    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting total patients count: ', error);
    throw new Error('Failed to get total patients count');
  }
};

export const getTotalScans = async (doctorId: string): Promise<number> => {
  try {
    const q = query(patientImagesCollection, where('doctorId', '==', doctorId));
    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error('Error getting total scans count: ', error);
    throw new Error('Failed to get total scans count');
  }
};

export const getIstemFormuCounts = async (
  doctorId: string,
): Promise<{ waiting: number; completed: number }> => {
  try {
    // Query the user's specific istem-formu subcollection instead of using collectionGroup
    const userIstemFormuCollection = collection(db, 'users', doctorId, 'istem-formu');
    
    const waitingQuery = query(
      userIstemFormuCollection,
      where('status', '==', 'pending'),
    );
    const completedQuery = query(
      userIstemFormuCollection,
      where('status', '==', 'completed'),
    );

    const waitingSnapshot = await getCountFromServer(waitingQuery);
    const completedSnapshot = await getCountFromServer(completedQuery);

    return {
      waiting: waitingSnapshot.data().count,
      completed: completedSnapshot.data().count,
    };
  } catch (error) {
    console.error('Error getting istem formu counts: ', error);
    throw new Error('Failed to get istem formu counts');
  }
}; 