import { db } from '../firebase';
import { collection, addDoc, getDocs, query, orderBy, where, limit, updateDoc, deleteDoc, doc, startAfter, getCountFromServer } from 'firebase/firestore';
import { PendingUser } from '../types';
import { convertTimestamps } from '../utils';

interface PaginationParams {
  page: number;
  pageSize: number;
  searchQuery?: string;
  status?: 'pending' | 'approved' | 'rejected';
}

interface PaginationResult {
  users: PendingUser[];
  total: number;
}

export const pendingUserService = {
  async create(pendingUser: Omit<PendingUser, 'id' | 'createdAt' | 'updatedAt' | 'status'>) {
    const cleanData = Object.fromEntries(
      Object.entries(pendingUser).filter(([, value]) => value !== undefined)
    );
    
    const docRef = await addDoc(collection(db, 'pendingUsers'), {
      ...cleanData,
      status: 'pending' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return docRef.id;
  },

  async getAll(): Promise<PendingUser[]> {
    const q = query(
      collection(db, 'pendingUsers'),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser
    );
  },

  async getByStatus(status: 'pending' | 'approved' | 'rejected'): Promise<PendingUser[]> {
    const q = query(
      collection(db, 'pendingUsers'),
      where('status', '==', status)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser
    );
  },

  async getPaginated({ page, pageSize, searchQuery = '', status = 'pending' }: PaginationParams): Promise<PaginationResult> {
    let q = query(
      collection(db, 'pendingUsers'),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );

    // Get total count
    const countQuery = query(
      collection(db, 'pendingUsers'),
      where('status', '==', status)
    );
    const countSnapshot = await getCountFromServer(countQuery);
    const total = countSnapshot.data().count;

    // Apply pagination
    const offset = (page - 1) * pageSize;
    if (offset > 0) {
      // Get the document to start after
      const offsetQuery = query(
        collection(db, 'pendingUsers'),
        where('status', '==', status),
        orderBy('createdAt', 'desc'),
        limit(offset)
      );
      const offsetSnapshot = await getDocs(offsetQuery);
      const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
      if (lastDoc) {
        q = query(
          collection(db, 'pendingUsers'),
          where('status', '==', status),
          orderBy('createdAt', 'desc'),
          startAfter(lastDoc),
          limit(pageSize)
        );
      }
    } else {
      q = query(
        collection(db, 'pendingUsers'),
        where('status', '==', status),
        orderBy('createdAt', 'desc'),
        limit(pageSize)
      );
    }

    const querySnapshot = await getDocs(q);
    let users = querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser
    );

    // Apply search filter if provided
    if (searchQuery.trim()) {
      const lowercaseSearch = searchQuery.toLowerCase();
      users = users.filter(user => 
        user.firstName.toLowerCase().includes(lowercaseSearch) ||
        user.lastName.toLowerCase().includes(lowercaseSearch) ||
        user.email.toLowerCase().includes(lowercaseSearch) ||
        user.phone.includes(searchQuery) ||
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(lowercaseSearch)
      );
    }

    return { users, total };
  },

  async getByEmail(email: string): Promise<PendingUser | null> {
    const q = query(
      collection(db, 'pendingUsers'),
      where('email', '==', email),
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return convertTimestamps({ id: doc.id, ...doc.data() }) as PendingUser;
    }
    return null;
  },

  async update(id: string, updates: Partial<PendingUser>) {
    const docRef = doc(db, 'pendingUsers', id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date(),
    });
  },

  async delete(id: string) {
    const docRef = doc(db, 'pendingUsers', id);
    await deleteDoc(docRef);
  },
}; 