import { db, auth } from '../firebase';
import { collection, doc, setDoc, getDocs, getDoc, updateDoc, deleteDoc, query, where, orderBy, limit, startAfter, getCountFromServer } from 'firebase/firestore';
import { PendingUser, User } from '../types';
import { convertTimestamps } from '../utils';

interface PaginationParams {
  page: number;
  pageSize: number;
  searchQuery?: string;
  role?: User['role'];
  clinic?: string;
}

interface PaginationResult {
  users: User[];
  total: number;
}

export const userService = {
  async createWithId(uid: string | null, userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>) {
    if (!uid) {
      throw new Error('UID is required to create user profile with deterministic ID');
    }
    const docRef = doc(db, 'users', uid);
    await setDoc(docRef, {
      ...userData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return uid;
  },

  async createFromPendingUser(pendingUser: PendingUser) {
    const userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'> = {
      firstName: pendingUser.firstName,
      lastName: pendingUser.lastName,
      email: pendingUser.email,
      phone: pendingUser.phone,
      address: pendingUser.address ?? null,
      birthDate: pendingUser.birthDate ?? null,
      role: pendingUser.role,
      clinic: pendingUser.clinic,
    };
    const uid = auth.currentUser?.uid ?? null;
    const userId = await this.createWithId(uid, userData);
    return userId;
  },

  async getById(id: string): Promise<User | null> {
    const docRef = doc(db, 'users', id);
    const docSnap = await getDoc(docRef);
    if (docSnap.exists()) {
      return convertTimestamps({ id: docSnap.id, ...docSnap.data() }) as User;
    }
    return null;
  },

  async getByEmail(email: string): Promise<User | null> {
    const q = query(
      collection(db, 'users'),
      where('email', '==', email),
      limit(1)
    );
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      return convertTimestamps({ id: doc.id, ...doc.data() }) as User;
    }
    return null;
  },

  async getAll(): Promise<User[]> {
    const q = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as User
    );
  },

  async getPaginated({ page, pageSize, searchQuery = '', role, clinic }: PaginationParams): Promise<PaginationResult> {
    let q = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc')
    );

    // Add filters if provided
    if (role) {
      q = query(q, where('role', '==', role));
    }
    if (clinic) {
      q = query(q, where('clinic', '==', clinic));
    }

    // Get total count
    const countQuery = role || clinic ? q : query(collection(db, 'users'));
    const countSnapshot = await getCountFromServer(countQuery);
    const total = countSnapshot.data().count;

    // Apply pagination
    const offset = (page - 1) * pageSize;
    if (offset > 0) {
      // Get the document to start after
      const offsetQuery = role || clinic ? q : query(
        collection(db, 'users'),
        orderBy('createdAt', 'desc')
      );
      const offsetSnapshot = await getDocs(offsetQuery);
      const lastDoc = offsetSnapshot.docs[offsetSnapshot.docs.length - 1];
      if (lastDoc) {
        q = query(q, startAfter(lastDoc), limit(pageSize));
      }
    } else {
      q = query(q, limit(pageSize));
    }

    const querySnapshot = await getDocs(q);
    let users = querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as User
    );

    // Apply search filter if provided
    if (searchQuery.trim()) {
      const lowercaseSearch = searchQuery.toLowerCase();
      users = users.filter(user => 
        user.firstName.toLowerCase().includes(lowercaseSearch) ||
        user.lastName.toLowerCase().includes(lowercaseSearch) ||
        user.email.toLowerCase().includes(lowercaseSearch) ||
        user.phone.includes(searchQuery) ||
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(lowercaseSearch)
      );
    }

    return { users, total };
  },

  async getByRole(role: User['role']): Promise<User[]> {
    const q = query(
      collection(db, 'users'),
      where('role', '==', role),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as User
    );
  },

  async getByClinic(clinic: string): Promise<User[]> {
    const q = query(
      collection(db, 'users'),
      where('clinic', '==', clinic),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => 
      convertTimestamps({ id: doc.id, ...doc.data() }) as User
    );
  },

  async update(id: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>) {
    const docRef = doc(db, 'users', id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date(),
    });
  },

  async delete(id: string) {
    const docRef = doc(db, 'users', id);
    await deleteDoc(docRef);
  },

  async searchByName(searchTerm: string): Promise<User[]> {
    const users = await this.getAll();
    const lowercaseSearch = searchTerm.toLowerCase();
    return users.filter(user => 
      user.firstName.toLowerCase().includes(lowercaseSearch) ||
      user.lastName.toLowerCase().includes(lowercaseSearch) ||
      `${user.firstName} ${user.lastName}`.toLowerCase().includes(lowercaseSearch)
    );
  },
}; 