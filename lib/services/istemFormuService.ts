import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  collectionGroup,
  Query,
  Timestamp,
} from 'firebase/firestore';
import { IstemFormuData } from '../types';
import { Patient } from '../types';

/**
 * Saves a new istem formu to the database.
 * @param istemFormuData - The data for the new istem formu, excluding id, createdAt, and status.
 * @param userId - The ID of the user creating the form.
 * @returns The ID of the newly created istem formu.
 * @throws Will throw an error if the form fails to save.
 */
export const saveIstemFormu = async (istemFormuData: Omit<IstemFormuData, 'id' | 'createdAt' | 'status'>, userId: string): Promise<string> => {
  try {
    const istemFormuCollection = collection(db, 'users', userId, 'istem-formu');

    const processedData: Omit<IstemFormuData, 'id'> = {
      ...istemFormuData,
      status: 'pending',
      createdAt: new Date(),
    };

    const docRef = await addDoc(istemFormuCollection, processedData);
    return docRef.id;
  } catch (error) {
    console.error('Error saving istem formu: ', error);
    throw new Error('Failed to save istem formu');
  }
};

/**
 * Retrieves waiting istem formu records for a specific patient for a non-admin user.
 * @param patientId - The ID of the patient to fetch forms for.
 * @param userId - The ID of the user requesting the forms.
 * @returns A promise that resolves to an array of waiting istem formu data.
 */
export const getWaitingIstemFormuByPatientForUser = async (patientId: string, userId: string): Promise<IstemFormuData[]> => {
  try {
    const istemFormuCollection = collection(db, 'users', userId, 'istem-formu');

    const waitingQuery = query(
      istemFormuCollection,
      where('patientId', '==', patientId),
      where('status', '==', 'pending')
    );

    const querySnapshot = await getDocs(waitingQuery);

    const userForms = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      userId: userId,
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as IstemFormuData[];

    // Sort by creation date (newest first)
    const sortedForms = userForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    return sortedForms;
  } catch (userError) {
    console.error('Error querying user subcollection for waiting forms:', userError);
    return [];
  }
};

/**
 * Retrieves waiting istem formu records for a specific patient for an admin user.
 * This function fetches forms from all users.
 * @param patientId - The ID of the patient to fetch forms for.
 * @returns A promise that resolves to an array of waiting istem formu data from all users.
 * @throws Will throw an error if fetching fails.
 */
export const getWaitingIstemFormuByPatientForAdmin = async (patientId: string): Promise<IstemFormuData[]> => {
  try {
    const allForms: IstemFormuData[] = [];
    const usersSnapshot = await getDocs(collection(db, 'users'));

    for (const userDoc of usersSnapshot.docs) {
      const istemFormuCollection = collection(db, 'users', userDoc.id, 'istem-formu');
      const waitingQuery = query(
        istemFormuCollection,
        where('patientId', '==', patientId),
        where('status', '==', 'pending')
      );

      const querySnapshot = await getDocs(waitingQuery);
      const userForms = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        userId: userDoc.id,
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as IstemFormuData[];

      allForms.push(...userForms);
    }

    // Sort by creation date (newest first)
    const sortedForms = allForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    return sortedForms;
  } catch (error) {
    console.error('Error fetching waiting istem formu for admin: ', error);
    throw new Error('Failed to fetch waiting istem formu for admin');
  }
};

/**
 * Retrieves completed istem formu records for a specific patient for a non-admin user.
 * @param patientId - The ID of the patient to fetch forms for.
 * @param userId - The ID of the user requesting the forms.
 * @returns A promise that resolves to an array of completed istem formu data.
 */
export const getCompletedIstemFormuByPatientForUser = async (patientId: string, userId: string): Promise<IstemFormuData[]> => {
  try {
    const istemFormuCollection = collection(db, 'users', userId, 'istem-formu');
    const completedQuery = query(
      istemFormuCollection,
      where('patientId', '==', patientId),
      where('status', '==', 'completed')
    );

    const querySnapshot = await getDocs(completedQuery);

    const userForms = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      userId: userId,
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      completedAt: doc.data().completedAt?.toDate() || new Date()
    })) as IstemFormuData[];

    const sortedForms = userForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    return sortedForms;
  } catch (userError) {
    console.error('Error querying user subcollection for completed forms:', userError);
    return [];
  }
};

/**
 * Retrieves completed istem formu records for a specific patient for an admin user.
 * This function fetches forms from all users.
 * @param patientId - The ID of the patient to fetch forms for.
 * @returns A promise that resolves to an array of completed istem formu data from all users.
 * @throws Will throw an error if fetching fails.
 */
export const getCompletedIstemFormuByPatientForAdmin = async (patientId: string): Promise<IstemFormuData[]> => {
  try {
    const allForms: IstemFormuData[] = [];
    const usersSnapshot = await getDocs(collection(db, 'users'));

    for (const userDoc of usersSnapshot.docs) {
      const istemFormuCollection = collection(db, 'users', userDoc.id, 'istem-formu');
      const completedQuery = query(
        istemFormuCollection,
        where('patientId', '==', patientId),
        where('status', '==', 'completed')
      );

      const querySnapshot = await getDocs(completedQuery);
      const userForms = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        userId: userDoc.id,
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        completedAt: doc.data().completedAt?.toDate() || new Date()
      })) as IstemFormuData[];

      allForms.push(...userForms);
    }

    // Sort by creation date (newest first)
    const sortedForms = allForms.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    return sortedForms;
  } catch (error) {
    console.error('Error fetching completed istem formu for admin: ', error);
    throw new Error('Failed to fetch completed istem formu for admin');
  }
};

/**
 * Updates the status of an istem formu to 'completed' and attaches scan image URLs.
 * @param formId - The ID of the istem formu to complete.
 * @param userId - The ID of the user who owns the form.
 * @param scanImages - An array of URLs for the uploaded scan images.
 * @returns A promise that resolves when the form is successfully updated.
 * @throws Will throw an error if the update fails.
 */
export const completeIstemFormu = async (
  formId: string, 
  userId: string, 
  scanImages: string[]
): Promise<void> => {
  try {
    const formRef = doc(db, 'users', userId, 'istem-formu', formId);
    
    await updateDoc(formRef, {
      status: 'completed',
      images: scanImages,
      completedAt: new Date()
    });
  } catch (error) {
    console.error('Error completing istem formu: ', error);
    throw new Error('Failed to complete istem formu');
  }
};

/**
 * Deletes an istem formu from the database.
 * @param formId - The ID of the istem formu to delete.
 * @param userId - The ID of the user who owns the form.
 * @returns A promise that resolves when the form is successfully deleted.
 * @throws Will throw an error if the deletion fails.
 */
export const deleteIstemFormu = async (formId: string, userId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'users', userId, 'istem-formu', formId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting istem formu:', error);
    throw new Error('Failed to delete istem formu');
  }
};

/**
 * Interface for the parameters required for paginated istem formu fetching.
 */
export interface GetIstemFormuPaginatedParams {
  userId?: string;
  page?: number;
  pageSize?: number;
  searchQuery?: string;
  statusFilter?: string;
}

/**
 * Interface for the result of a paginated istem formu fetch.
 */
export interface PaginatedIstemFormuResult {
  forms: IstemFormuData[];
  total: number;
}

/**
 * Retrieves a paginated list of istem formu records for a specific non-admin user.
 * @param params - The pagination and filter parameters.
 * @returns A promise that resolves to an object containing the forms and total count.
 * @throws Will throw an error if the fetch fails or if userId is not provided.
 */
export const getIstemFormuPaginatedForUser = async (params: GetIstemFormuPaginatedParams): Promise<PaginatedIstemFormuResult> => {
  const {
    userId,
    page = 1,
    pageSize = 10,
    searchQuery,
    statusFilter,
  } = params;

  if (!userId) {
    throw new Error("User ID is required for non-admin paginated fetch.");
  }

  try {
    let forms: IstemFormuData[] = [];
    
    const formsCollection = collection(db, 'users', userId, 'istem-formu');
    const formsQuery = query(formsCollection, orderBy('createdAt', 'desc'));
    
    const formsSnapshot = await getDocs(formsQuery);
    forms = formsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      userId: userId, // a user's form is under their own id
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as IstemFormuData[];

    if (statusFilter && statusFilter !== 'Tüm Durumlar') {
      forms = forms.filter(form => form.status === statusFilter);
    }

    if (searchQuery) {
      const patients = await getDocs(collection(db, 'patients'));
      const patientMap = new Map(patients.docs.map(doc => [doc.id, doc.data() as Patient]));

      forms = forms.filter(form => {
        const patient = patientMap.get(form.patientId);
        const patientName = patient ? `${patient.firstName} ${patient.lastName}` : '';
        return patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
               form.diagnosis.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    const total = forms.length;
    const paginatedForms = forms.slice((page - 1) * pageSize, page * pageSize);

    return { forms: paginatedForms, total };
  } catch (error) {
    console.error('Error getting paginated istem formu for user:', error);
    throw new Error('Failed to get paginated istem formu for user');
  }
};

/**
 * Retrieves a paginated list of all istem formu records for an admin user using a collection group query.
 * This is more efficient as it doesn't require fetching all users first.
 * NOTE: This requires a composite index in Firestore on the 'istem-formu' collection group.
 * The index should be on 'createdAt' (descending).
 * @param params - The pagination and filter parameters, excluding userId.
 * @returns A promise that resolves to an object containing the forms and total count.
 * @throws Will throw an error if the fetch fails.
 */
export const getIstemFormuPaginatedForAdmin = async (params: Omit<GetIstemFormuPaginatedParams, 'userId'>): Promise<PaginatedIstemFormuResult> => {
  const {
    page = 1,
    pageSize = 10,
    searchQuery,
    statusFilter,
  } = params;

  try {
    // collectionGroup query is more efficient for admins
    const formsCollectionGroup = collectionGroup(db, 'istem-formu');
    
    // Initial query, sorted by date
    let formsQuery: Query = query(formsCollectionGroup, orderBy('createdAt', 'desc'));

    // Apply filters. Note: Firestore requires an index for this.
    // We will apply search filtering on the client side after fetching, as full-text search is complex with Firestore.
    if (statusFilter && statusFilter !== 'Tüm Durumlar') {
      formsQuery = query(formsQuery, where('status', '==', statusFilter));
    }

    const allFormsSnapshot = await getDocs(formsQuery);

    let allForms = allFormsSnapshot.docs.map(doc => {
      const data = doc.data();
      // The path of a doc in a collection group is 'users/{userId}/istem-formu/{formId}'
      const userId = doc.ref.parent.parent?.id;
      return {
        id: doc.id,
        ...data,
        userId: userId,
        createdAt: (data.createdAt as Timestamp)?.toDate() || new Date()
      } as IstemFormuData;
    });

    // Apply search query filtering after fetching
    if (searchQuery) {
      const patients = await getDocs(collection(db, 'patients'));
      const patientMap = new Map(patients.docs.map(doc => [doc.id, doc.data() as Patient]));

      allForms = allForms.filter(form => {
        const patient = patientMap.get(form.patientId);
        const patientName = patient ? `${patient.firstName} ${patient.lastName}` : '';
        return patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
               form.diagnosis.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    const total = allForms.length;
    const paginatedForms = allForms.slice((page - 1) * pageSize, page * pageSize);

    return { forms: paginatedForms, total };

  } catch (error) {
    const firebaseError = error as { code?: string };
    console.error('Error getting paginated istem formu for admin:', error);
    
    if (firebaseError.code === 'failed-precondition') {
      console.error(
        'Firestore index missing. Please create a composite index for the "istem-formu" collection group.'
      );
      throw new Error(
        'Veritabanı hatası: Gerekli indeks bulunamadı. Lütfen yönetici ile iletişime geçin.'
      );
    }
    
    throw new Error('Failed to get paginated istem formu for admin');
  }
}; 