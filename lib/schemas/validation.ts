import { z } from 'zod';

// Reusable birth date validation with Turkish error messages
const birthDateValidation = (date: Date | undefined) => {
  if (!date) return true;
  const today = new Date();
  return date <= today;
};

const birthDateRangeValidation = (date: Date | undefined) => {
  if (!date) return true;
  const today = new Date();
  const minAge = new Date(today.getFullYear() - 120, today.getMonth(), today.getDate());
  return date >= minAge;
};

// Schema for pending user creation (signup form)
export const pendingUserSchema = z.object({
  firstName: z.string()
    .min(1, 'Ad alanı zorunludur')
    .min(3, 'Ad en az 3 karakter olmalıdır')
    .max(50, 'Ad en fazla 50 karakter olabilir')
    .trim(),
  lastName: z.string()
    .min(1, 'Soyad alanı zorunludur')
    .min(3, 'Soyad en az 3 karakter olmalıdır')
    .max(50, 'Soyad en fazla 50 karakter olabilir')
    .trim(),
  email: z.string()
    .email('Geçerli bir e-posta adresi girin')
    .max(100, 'E-posta en fazla 100 karakter olabilir')
    .toLowerCase()
    .trim(),
  phone: z.string()
    .min(1, 'Telefon numarası zorunludur')
    .min(10, 'Geçerli bir telefon numarası girin')
    .max(20, 'Telefon numarası en fazla 20 karakter olabilir')
    .trim(),
  address: z.string()
    .max(200, 'Adres en fazla 200 karakter olabilir')
    .trim()
    .optional(),
  birthDate: z.date()
    .optional()
    .refine(birthDateValidation, {
      message: 'Doğum tarihi gelecekte olamaz'
    })
    .refine(birthDateRangeValidation, {
      message: 'Doğum tarihi mantıklı bir aralıkta olmalıdır'
    }),
  role: z.enum(['doctor', 'assistant'], {
    required_error: 'Rol seçimi zorunludur',
    invalid_type_error: 'Rol doktor veya asistan olmalıdır'
  }),
  clinic: z.string()
    .min(1, 'Klinik adı zorunludur')
    .max(100, 'Klinik adı en fazla 100 karakter olabilir')
    .trim()
});

// Schema for user profile creation
export const userProfileSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .min(3, 'First name must be at least 3 characters')
    .max(50, 'First name must be 50 characters or less')
    .trim(),
  lastName: z.string()
    .min(1, 'Last name is required')
    .min(3, 'Last name must be at least 3 characters')
    .max(50, 'Last name must be 50 characters or less')
    .trim(),
  email: z.string()
    .email('Invalid email format')
    .max(100, 'Email must be 100 characters or less')
    .toLowerCase()
    .trim(),
  phone: z.string()
    .min(1, 'Phone number is required')
    .min(10, 'Geçerli bir telefon numarası girin')
    .max(20, 'Phone number must be 20 characters or less')
    .trim(),
  address: z.string()
    .max(200, 'Address must be 200 characters or less')
    .trim()
    .optional()
    .default(''),
  birthDate: z.date()
    .optional()
    .default(() => new Date())
    .refine(birthDateValidation, {
      message: 'Doğum tarihi gelecekte olamaz'
    })
    .refine(birthDateRangeValidation, {
      message: 'Doğum tarihi mantıklı bir aralıkta olmalıdır'
    }),
  role: z.enum(['doctor', 'assistant']).default('doctor'),
  clinic: z.string()
    .max(100, 'Clinic name must be 100 characters or less')
    .trim()
    .optional()
    .default('')
});

// Schema for user profile updates (includes role changes) - Turkish validation
export const userProfileUpdateSchema = z.object({
  firstName: z.string()
    .min(1, 'Ad alanı zorunludur')
    .min(3, 'Ad en az 3 karakter olmalıdır')
    .max(50, 'Ad en fazla 50 karakter olabilir')
    .trim(),
  lastName: z.string()
    .min(1, 'Soyad alanı zorunludur')
    .min(3, 'Soyad en az 3 karakter olmalıdır')
    .max(50, 'Soyad en fazla 50 karakter olabilir')
    .trim(),
  phone: z.string()
    .min(1, 'Telefon numarası zorunludur')
    .min(10, 'Geçerli bir telefon numarası girin')
    .max(20, 'Telefon numarası en fazla 20 karakter olabilir')
    .trim(),
  address: z.string()
    .max(200, 'Adres en fazla 200 karakter olabilir')
    .trim()
    .optional(),
  birthDate: z.date()
    .optional()
    .refine(birthDateValidation, {
      message: 'Doğum tarihi gelecekte olamaz'
    })
    .refine(birthDateRangeValidation, {
      message: 'Doğum tarihi mantıklı bir aralıkta olmalıdır'
    }),
  role: z.enum(['doctor', 'assistant'], {
    required_error: 'Rol seçimi zorunludur',
    invalid_type_error: 'Rol doktor veya asistan olmalıdır'
  }),
  clinic: z.string()
    .max(100, 'Klinik adı en fazla 100 karakter olabilir')
    .trim()
    .optional()
});

// Schema for password validation
export const passwordSchema = z.string()
  .min(8, 'Şifre en az 8 karakter uzunluğunda olmalıdır')
  .max(128, 'Şifre en fazla 128 karakter olabilir')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
    'Şifre en az bir küçük harf, bir büyük harf ve bir rakam içermelidir');

// Schema for registration completion
export const registrationCompletionSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// Schema for email validation
export const emailSchema = z.string()
  .email('Invalid email format')
  .max(100, 'Email must be 100 characters or less')
  .toLowerCase()
  .trim();

// Schema for JWT token verification request
export const tokenVerificationSchema = z.object({
  token: z.string().min(1, 'Token is required')
});

// Schema for admin claim setting
export const adminClaimSchema = z.object({
  uid: z.string().min(1, 'User UID is required'),
  isAdmin: z.boolean()
});

export const patientFormSchema = z.object({
  firstName: z.string()
    .min(1, 'Ad alanı zorunludur')
    .min(3, 'Ad en az 3 karakter olmalıdır')
    .max(50, 'Ad en fazla 50 karakter olabilir')
    .trim(),
  lastName: z.string()
    .min(1, 'Soyad alanı zorunludur')
    .min(3, 'Soyad en az 3 karakter olmalıdır')
    .max(50, 'Soyad en fazla 50 karakter olabilir')
    .trim(),
  tcKimlik: z.string()
    .min(1, 'TC Kimlik No zorunludur')
    .length(11, 'TC Kimlik No 11 haneli olmalıdır')
    .regex(/^\d+$/, 'TC Kimlik No sadece rakamlardan oluşmalıdır')
    .trim(),
  birthDate: z.date({ required_error: 'Doğum Tarihi zorunludur' })
    .refine(birthDateValidation, {
      message: 'Doğum tarihi gelecekte olamaz'
    })
    .refine(birthDateRangeValidation, {
      message: 'Doğum tarihi mantıklı bir aralıkta olmalıdır'
    }),
  gender: z.enum(['Erkek', 'Kadın'], {
    errorMap: (issue, ctx) => {
      if (issue.code === 'invalid_enum_value') {
        return { message: 'Lütfen geçerli bir cinsiyet seçin.' };
      }
      return { message: ctx.defaultError };
    }
  }),
  phone: z.string()
    .min(1, 'Telefon numarası zorunludur')
    .min(10, 'Geçerli bir telefon numarası girin')
    .max(20, 'Telefon numarası en fazla 20 karakter olabilir')
    .trim(),
  email: z.string()
    .email('Geçerli bir e-posta adresi girin')
    .max(100, 'E-posta en fazla 100 karakter olabilir')
    .toLowerCase()
    .trim()
    .optional()
    .or(z.literal('')),
  address: z.string()
    .max(200, 'Adres en fazla 200 karakter olabilir')
    .trim()
    .optional(),
});

export type PatientFormData = z.infer<typeof patientFormSchema>;

export const loginSchema = z.object({
  email: z.string().email({ message: "Lütfen geçerli bir e-posta adresi girin." }),
  password: z.string().min(1, { message: "Lütfen şifrenizi girin." }),
});

export type LoginFormInputs = z.infer<typeof loginSchema>;

export const completeRegistrationSchema = z.object({
  name: z.string().min(1, { message: "İsim alanı zorunludur." }),
  clinicName: z.string().min(1, { message: "Klinik adı zorunludur." }),
  phone: z.string()
    .min(1, { message: "Telefon alanı zorunludur." })
    .min(10, { message: "Geçerli bir telefon numarası girin" }),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, { message: "Mevcut şifre zorunludur." }),
  newPassword: passwordSchema,
  confirmPassword: z.string().min(1, { message: "Yeni şifreyi onaylayın." }),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Yeni şifreler eşleşmiyor.",
  path: ["confirmPassword"],
});

export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;

// Export types for use throughout the application
export type PendingUserInput = z.infer<typeof pendingUserSchema>;
export type UserProfileInput = z.infer<typeof userProfileSchema>;
export type UserProfileUpdateInput = z.infer<typeof userProfileUpdateSchema>;
export type RegistrationCompletionInput = z.infer<typeof registrationCompletionSchema>;
export type EmailInput = z.infer<typeof emailSchema>;
export type TokenVerificationInput = z.infer<typeof tokenVerificationSchema>;
export type AdminClaimInput = z.infer<typeof adminClaimSchema>; 