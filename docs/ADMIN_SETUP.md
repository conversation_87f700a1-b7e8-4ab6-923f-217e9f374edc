# User Approval Process

When you approve a pending user:
1. The pending user status is updated to "approved"
2. A **secure JWT token** is generated containing the user's information
3. The user will recieve an email containing the link to finish their account setup
4. They'll enter their password and activate their account (their info is pre-filled from the secure token)

### Complete Registration Flow

After approval, users receive a **secure registration link** with a cryptographically signed JWT token:
1. <PERSON><PERSON> approves a user and gets a unique, secure registration link
2. <PERSON><PERSON> shares this link with the approved user via email
3. User clicks the link to go to the registration completion page
4. The system verifies the JWT token to ensure it's valid and not expired
5. User sets up their password (their info is pre-filled from the verified token)
6. Account is automatically created and activated

Once completed:
- A Firebase Authentication user is created
- They can log in normally through the login page




**Enhanced Security Features:**
- **JWT Token Encryption**: Each link contains a cryptographically signed JWT token
- **Token Expiration**: Links automatically expire after 48 hours
- **User-Specific**: Each token is unique to the specific user and cannot be reused
- **Tamper-Proof**: Any modification to the token makes it invalid
- **Server-Side Verification**: Tokens are verified server-side using Firebase Functions
- **No Data Exposure**: User data is securely encoded within the signed token



## Production Recommendations

1. **Use a strong JWT secret** (minimum 32 characters)
2. **Enable 2FA** in Firebase Console for additional security
3. **Limit admin access** to trusted personnel only
4. **Regular monitoring** of admin activities and token usage
5. **Backup user data** regularly
6. **Monitor token expiration** and renewal patterns
7. **Set up alerts** for failed token verifications
8. **Review security logs** regularly for suspicious activity 